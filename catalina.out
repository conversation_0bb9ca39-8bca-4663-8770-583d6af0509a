20-Aug-2025 04:42:36.562 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version:        Apache Tomcat/8.5.13
20-Aug-2025 04:42:36.576 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server built:          Mar 27 2017 14:25:04 UTC
20-Aug-2025 04:42:36.576 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server number:         8.5.13.0
20-Aug-2025 04:42:36.576 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Name:               Linux
20-Aug-2025 04:42:36.578 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Version:            6.1.129-138.220.amzn2023.x86_64
20-Aug-2025 04:42:36.578 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Architecture:          amd64
20-Aug-2025 04:42:36.579 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Java Home:             /usr/lib/jvm/java-1.8.0-amazon-corretto.x86_64/jre
20-Aug-2025 04:42:36.579 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Version:           1.8.0_462-b08
20-Aug-2025 04:42:36.579 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Vendor:            Amazon.com Inc.
20-Aug-2025 04:42:36.580 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:         /u01/apache-tomcat-8.5.13
20-Aug-2025 04:42:36.580 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:         /u01/apache-tomcat-8.5.13
20-Aug-2025 04:42:36.581 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.config.file=/u01/apache-tomcat-8.5.13/conf/logging.properties
20-Aug-2025 04:42:36.581 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
20-Aug-2025 04:42:36.581 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djdk.tls.ephemeralDHKeySize=2048
20-Aug-2025 04:42:36.582 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
20-Aug-2025 04:42:36.583 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.base=/u01/apache-tomcat-8.5.13
20-Aug-2025 04:42:36.583 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.home=/u01/apache-tomcat-8.5.13
20-Aug-2025 04:42:36.584 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.io.tmpdir=/u01/apache-tomcat-8.5.13/temp
20-Aug-2025 04:42:36.584 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: /usr/java/packages/lib/amd64:/usr/lib64:/lib64:/lib:/usr/lib
20-Aug-2025 04:42:36.755 INFO [main] org.apache.coyote.AbstractProtocol.init Initializing ProtocolHandler ["ajp-nio-8009"]
20-Aug-2025 04:42:36.791 INFO [main] org.apache.tomcat.util.net.NioSelectorPool.getSharedSelector Using a shared selector for servlet write/read
20-Aug-2025 04:42:36.795 INFO [main] org.apache.catalina.startup.Catalina.load Initialization processed in 1165 ms
20-Aug-2025 04:42:36.851 INFO [main] org.apache.catalina.core.StandardService.startInternal Starting service Catalina
20-Aug-2025 04:42:36.852 INFO [main] org.apache.catalina.core.StandardEngine.startInternal Starting Servlet Engine: Apache Tomcat/8.5.13
20-Aug-2025 04:42:36.867 INFO [ws-startStop-1] org.apache.catalina.startup.HostConfig.deployDescriptor Deploying configuration descriptor /u01/apache-tomcat-8.5.13/conf/Catalina/ws/ROOT.xml
20-Aug-2025 04:42:36.925 INFO [ws-startStop-1] org.apache.catalina.startup.ExpandWar.expand An expanded directory [/u01/apache-tomcat-8.5.13/webapps/ROOT] was found with a last modified time that did not match the associated WAR. It will be deleted.
20-Aug-2025 04:42:51.337 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_gradeSelector_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.339 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_gradeSelectorScript_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.341 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_navheader_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.342 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_navheader_new_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.344 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_navheader_new_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.345 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_notesHighlightSection_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.347 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_qaSection_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.348 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_quizSection_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.349 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_quizSection_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.350 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_readerView_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.350 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_readerView_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.351 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_readingSection_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.352 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_readingSection_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.353 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_reviewrating_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.354 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_reviewrating_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.354 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_revision_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.355 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_revision_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.356 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_slider_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.357 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_slider_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.357 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_testgenModal_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.358 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_testgenModal_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.359 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_testgeneratorscripts_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.359 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_testgeneratorscripts_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.360 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_uploadResourcesModal_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.361 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_uploadResourcesModal_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.362 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_videoPlay_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.363 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_videoPlay_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.364 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_videoSection_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.364 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_videoSection_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.365 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_weblinksSection_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.366 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_weblinksSection_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.367 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_wonderGoaStudySet_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.367 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_wonderGoaStudySet_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.368 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_wsFooter_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.369 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublish_wsFooter_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.370 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishanswerFixerInput_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.371 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishanswerFixerInput_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.371 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishanswerFixerProcessor_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.372 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishanswerFixerProcessor_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.373 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishanswerMatch_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.374 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishanswerMatch_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.374 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublisharihant_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.375 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublisharihant_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.376 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishauthorDetails_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.376 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishauthorDetails_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.377 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishbannerManagement_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.378 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishbannerManagement_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.378 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishbook_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.379 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishbook_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.381 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishbookCreate_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.381 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishbookCreate_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.382 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishbookCreateNew_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.383 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishbookCreateNew_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.384 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishbookdtl_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.384 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishbookdtl_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.385 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishdeleteAccount_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.386 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishdeleteAccount_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.386 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishdemoHome_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.387 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishdemoHome_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.388 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublisherrorPage_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.438 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublisherrorPage_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.439 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishindependentContent_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.439 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishindependentContent_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.440 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishindex_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.441 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishindex_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.441 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishindexHome_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.442 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishindexHome_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.442 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishinstructorLibrary_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.443 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishinstructorLibrary_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.444 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishmanageExams_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.448 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishmanageExams_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.449 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishmanageTabs_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.451 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishmanageTabs_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.452 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishmybooks_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.454 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishmybooks_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.456 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishmylibrary_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.464 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishmylibrary_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.465 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishnotescreator_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.465 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishnotescreator_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.466 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishopenPDF_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.466 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishopenPDF_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.467 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishorders_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.467 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishorders_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.469 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishpdfReader_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.469 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishpdfReader_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.470 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishpubDesk_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.470 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishpubDesk_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.471 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishpubSales_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.472 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishpubSales_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.473 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishqandaCreator_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.473 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishqandaCreator_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.474 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishquizcreator_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.474 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishquizcreator_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.474 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishquizcreatorbulk_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.475 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishquizcreatorbulk_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.475 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishquizcreatorbulkinput_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.475 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishquizcreatorbulkinput_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.476 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishrelatedVideosAdmin_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.476 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishrelatedVideosAdmin_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.476 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishresetPasswordEmail_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.477 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishresetPasswordEmail_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.477 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishselfService_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.477 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishselfService_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.478 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishstudySet_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.478 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishstudySet_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.479 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishtestgenerator_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.479 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishtestgenerator_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.479 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishuploadTex_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.480 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishuploadTex_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.480 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishvideoExplanation_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.480 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishvideoExplanation_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.481 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishvideoplayer_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.481 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishvideoplayer_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.481 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishwseditor_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.482 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderpublishwseditor_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.482 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderslatekidsindex_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.483 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wonderslatekidsindex_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.483 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmain_aiContent_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.483 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmain_aiContent_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.484 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmain_aiOptions_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.484 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmain_aiOptions_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.489 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainaiBookDtl_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.489 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainaiBookDtl_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.489 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainaibook_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.490 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainaibook_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.490 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainbookai_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.490 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainbookai_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.491 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainindex_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.491 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainindex_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.492 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainmyLibrary_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.492 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainmyLibrary_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.493 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainqpcreator_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.493 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainqpcreator_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.493 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainqplist_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.494 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainqplist_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.494 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainqpprint_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.494 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainqpprint_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.495 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainqpview_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.495 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainqpview_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.496 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainunmarkedQA_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.499 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wpmainunmarkedQA_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.500 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsLibraryaccessCode_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.501 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsLibraryaccessCode_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.501 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsLibraryindex_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.501 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsLibraryindex_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.501 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsLibrarymyLibrary_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.502 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshop_blogEnglishDisplay_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.502 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshop_blogEnglishDisplay_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.503 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshop_cartScripts_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.503 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshop_cartScripts_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.503 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshop_searchScripts_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.504 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshop_searchScripts_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.504 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshop_shoppingCartLogic_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.504 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshop_shoppingCartLogic_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.505 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshop_videoCreator_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.505 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshop_videoCreator_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.506 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopaddGradeInfo_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.506 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopaddGradeInfo_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.507 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopamazonorderconfirmation_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.508 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopamazonorderconfirmation_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.508 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopblogEnglish_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.508 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopblogEnglish_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.509 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopblogHindi_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.509 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopblogHindi_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.513 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopcart_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.513 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopcart_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.515 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopmanageShopSpecials_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.515 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopmanageShopSpecials_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.516 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshoporderManagement_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.516 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshoporderManagement_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.516 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopvalidityExtensionAdmin_gsp_html.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.517 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp_wonderslate_qa_wsshopvalidityExtensionAdmin_gsp_linenumbers.data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.517 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.518 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/ibookso] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.521 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/usermanagement] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.521 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/discussion] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.522 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/admin] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.522 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/librarybooks] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.522 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/institute] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.522 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/logs] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.525 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/data] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.525 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/cache] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.526 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/groups] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.526 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/toDo] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.526 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/shop] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.527 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/comparison] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.527 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/WsLibrary] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.527 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/drive] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.528 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/qp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.528 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/report] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.528 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/log] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.529 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/prepjoy] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.529 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/harper] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.529 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/seo] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.530 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/learn] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.530 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/information] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.530 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/publish] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.530 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/discussions] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.531 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/publiclibrary] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.531 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/content] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.531 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/games] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.550 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/client] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.575 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/view] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.581 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/marketing] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:51.582 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/wonderslate/sqlutil] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.681 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/com/ibookso/products] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.698 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.702 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/folder/folderHome.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.704 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/libwonder/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.705 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/libwonder/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.706 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/libwonder/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.707 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/pdfReader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.708 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_allSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.710 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_notesHighlightSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.710 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_printBookSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.711 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_resourceContents.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.712 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_security.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.713 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/createFlashCards.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.713 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/mobilePdfReader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.714 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/contactus.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.715 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_ebookInclude.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.717 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/ebook.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.717 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_videoSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.718 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_shareContent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.719 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/displayFlashCards.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.719 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_wsOtherResource.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.725 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_bookTestSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.725 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/notesViewer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.727 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_weblinksSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.728 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_flashcardMatch.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.734 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_revision.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.735 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_displayChapterDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.735 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/quizcreator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.735 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_relatedBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.736 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/flpDemo.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.736 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/displayPdfMaterial.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.736 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_resourceChapterList.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.737 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/book.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.737 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/flashcardHome.css] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.737 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_cartScripts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.738 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.739 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_readSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.739 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/deleteChapters.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.739 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/flashCardHome.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.740 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/eBookDtl.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.740 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/createPdfBook.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.740 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_eBookSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.741 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_mobileResourceMenu.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.741 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_collectionBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.741 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_genericReader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.743 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_readerView.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.744 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_qaSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.744 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_eBookSection_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.744 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/previewChapter.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.745 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_ebookFeatures.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.746 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/editQuiz.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.747 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/pdfViewer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.747 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/epubReader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.748 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/_bookGPTSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.748 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/robots.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.749 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resources/childBookInput.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.750 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/discountManager.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.751 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/searchDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.751 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/printBooksDownloadPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.751 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/videoExplanationUpdate.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.752 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/informationPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.752 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/refundDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.754 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/managePublishers.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.754 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/accessCodeUsage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.755 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/jwplayerLive.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.755 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/cartPurchase.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.755 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/list.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.756 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/contentModeration.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.756 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/publisherDsize.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.756 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/dataRetriever.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.757 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/informationType.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.757 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/manageBatchUsers.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.758 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/priceList.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.758 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/userBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.758 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/_pomodoro.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.759 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/manageBookExpiry.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.759 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/insertjob.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.760 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/manageNotifications.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.760 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/emailWhitelist.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.760 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/externalOrders.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.761 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/moderation.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.761 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/deleteuser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.761 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/informationPageDtl.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.762 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/paymentDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.763 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/bookgptPromptAdmin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.763 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/_bookgptChatViewer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.763 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/_bookgptPDFViewer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.765 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/difficultyLevelMapping.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.765 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/admin/bookSupport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.768 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/salesDashboard/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.768 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/appVersionManagement.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.769 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/userAccess.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.769 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/unblockUser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.769 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/quizissues.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.770 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/notificationManagement.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.770 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/liveTestRanks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.770 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/userManagement.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.771 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/enquiryDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.771 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/enquiryForm.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.771 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/packageBooksReport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.772 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/deleteUserBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.772 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/notification.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.772 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/log/migrateuser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.773 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/karnataka.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.773 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/dailyCurrentAffairs.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.774 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/cacscma.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.774 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/ctet.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.774 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.775 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/prepJoyNew.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.775 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/enggentrances.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.775 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/quiz.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.777 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.777 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/affiliation.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.777 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.778 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/dailyTest.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.778 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/currentaffairs.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.778 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/quizAnalytics.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.779 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/leaderBoard.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.779 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/_prepjoySignup.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.779 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/currentAffairsDaily.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.780 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/currentAffairs.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.780 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/neet.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.781 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/eBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.781 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/history.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.781 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/joinGame.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.782 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/prepJoyGame.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.782 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/audioChat.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.782 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/_prepjoy-loader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.783 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/join.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.783 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/creator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.784 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/admin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.785 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepjoy/_quizResult.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.786 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/_instituteBanners.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.786 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/usageReportInstituteAdmin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.786 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/usageReport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.787 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/ntse.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.789 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/manageInstitutePage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.789 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/isbnKeyword.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.790 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/home.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.791 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/userLoginLimit.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.791 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/admin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.791 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/usageReportDoris.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.792 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.792 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/userManagement.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.792 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/manageClasses.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.793 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/registeredUserReport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.794 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/libAdmin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.794 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/recentInstitutes.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.795 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/libraryUserUploader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.796 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/usageReportTitleWise.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.796 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/downloadUsageReport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.797 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/bookCreateNew.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.802 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/usageReportCorporateWise.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.804 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/institute/instituteProduct.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.808 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.808 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.808 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/privacy.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.809 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.809 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/_storeHolder.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.812 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/_commonfooter_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.813 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/store1.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.814 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/contact.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.815 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/about.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.815 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.816 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.816 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.816 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/termsCondition.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.817 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/library.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.817 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/evidya/terms.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.818 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/whitelabel/_storeDisplay.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.818 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/whitelabel/_billingShipping.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.818 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/whitelabel/_shoppingCartDisplay.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.819 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/whitelabel/_storeLogic.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.819 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/whitelabel/migrateBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.819 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/whitelabel/recharge.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.820 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/whitelabel/setBookType.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.820 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/whitelabel/_aiStoreLogic.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.821 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/whitelabel/_aiStoreDisplay.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.822 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/whitelabel/_shoppingCartLogic.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.822 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wolterskluwer/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.823 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wolterskluwer/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.823 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wolterskluwer/_storeHolder.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.823 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wolterskluwer/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.824 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wolterskluwer/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.824 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wolterskluwer/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.824 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mtg/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.825 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mtg/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.825 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mtg/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.825 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mtg/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.826 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/appinapp/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.826 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/appinapp/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.827 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/appinapp/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.828 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/oswaal/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.828 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/oswaal/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.828 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/oswaal/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.829 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/oswaal/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.829 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/wrongPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.830 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/packageBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.830 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/faq.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.830 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/eduWonder.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.830 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_home_reportSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.830 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/schoolProducts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.831 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/sageLanding.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.831 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/books.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.831 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.831 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/description.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.832 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_leaderBoard.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.832 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/shoppingCart.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.832 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/home.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.834 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_moreOptions.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.835 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/ebooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.835 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.835 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/contactus.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.836 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/ebook.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.836 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_shareContent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.836 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_signup_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.837 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/vtulibrary.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.838 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/vtuanalytics.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.838 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.839 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/toppers.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.839 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/informationPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.839 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_home_latestQuiz.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.840 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/eBooksStoreIntegration.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.840 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/mylibrary.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.840 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_wsabout.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.840 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/myHome.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.841 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/careercounselling.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.841 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.841 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/book.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.842 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_signIn.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.842 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_home_sideBar.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.842 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_login.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.842 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.843 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/aboutus.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.843 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.843 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_home_institutes.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.844 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_home_searchSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.844 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/landingPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.844 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/bookdtl.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.847 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/checkout.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.848 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/dashboard.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.848 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_bannerSlider.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.848 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/leaderboard.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.849 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_pomodoro.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.849 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/directSales.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.849 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/appLink.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.850 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/vtubook.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.850 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/myActivity.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.850 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_bfooter.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.850 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/userOrderProcess.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.851 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/bookCreateNew.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.851 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/products.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.851 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_home_continueLearning.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.852 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/publishersProduct.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.852 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/informationPageDtl.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.852 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/vtu.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.853 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/teachersProduct.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.853 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/vtuadmin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.853 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/studentproblems.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.854 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/manageExams.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.854 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/pdftoepub.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.854 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/blogs.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.855 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/books/_ibookgpt-promotion.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.857 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sageUI/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.857 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sageUI/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.858 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sageUI/authorDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.859 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sageUI/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.860 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.860 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/myLibrary.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.861 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/unmarkedQA.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.861 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/_aiOptions.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.861 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/bookai.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.861 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/aibook.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.862 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/aiBookDtl.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.862 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/_aiContent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.862 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/qplist.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.863 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/qpprint.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.863 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/qpview.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.863 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wpmain/qpcreator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.864 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/autoVideo/automatedVideo.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.864 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/autoVideo/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.869 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/autoVideo/autoImageVideo.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.870 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/resetPassword.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.870 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/instituteWelcomeEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.871 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/etextsSendContactUs.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.871 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/welcomeModal.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.872 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/invoice2020.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.872 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/ebouquetUser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.872 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_welcomeModal.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.873 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/ccavRequestHandler.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.873 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_pageupload.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.873 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/radianBooksInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.874 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/wsinvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.874 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/winnersInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.874 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/wolterskluwerSuggestbookemail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.874 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userCreationEmailSage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.875 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/otpRequestEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.875 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_loggedactivities.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.875 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/otpRequestEmailArihant.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.876 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/edugorillaInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.876 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/otpRequestEmailLibwonder.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.876 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/etextsSendFeedback.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.876 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userCreationEmailEbouquet.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.877 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userCreationEmailEvidya.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.877 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/resetPasswordEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.877 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/wsuser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.877 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userBookPurchaseEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.878 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/blackspineInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.878 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/ebouquetSuggestbookemail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.878 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/etextsSuggestbookemail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.879 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.879 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/resetPasswordEmailEtexts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.879 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/ebouquetSuggestbookemailuser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.879 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userActiveCartEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.880 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/otpRequestEmailEbouquet.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.880 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/authordetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.880 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userProfile.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.880 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/instituteUserEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.881 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userPaidPreviewEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.881 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/jbclassInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.881 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/otpRequestEmailSage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.881 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/paymentPendingEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.882 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userCreationEmailEtexts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.882 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/enquiryFormEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.882 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/ebouquetRequestDemo.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.883 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_pageaddquiz.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.883 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_register.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.883 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/displayChapter.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.883 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/contentModerationEmailWS.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.884 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/mtgInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.884 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/oswaalInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.884 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userPurchasedBookEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.884 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/affiliationEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.885 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/list.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.885 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/evidyaSuggestbookemail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.885 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userBookPurchase.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.886 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/editProfile.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.886 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_addTopic.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.886 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_invoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.886 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/chapterDownloadError.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.887 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_changePasswordModal.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.887 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/invoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.887 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/returnPolicy.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.888 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/addResource.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.888 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_userProfile.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.888 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/oswalpublisherInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.888 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/ebouquetRetriever.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.889 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/externalPurchaseMail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.889 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/etextsSuggestbookemailuser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.889 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_pageaddlink.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.890 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/resetPasswordEmailSage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.890 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/evidyaSuggestbookemailuser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.890 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/approveContent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.890 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_userProfile_prepjoy.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.891 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/userCreationEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.891 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_contentCreator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.891 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_userProfileSage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.891 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/arihantInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.892 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/addUser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.892 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/privateLabelInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.892 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/prepjoyInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.893 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/resetPasswordEmailEvidya.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.893 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/_userProfile_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.893 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/purchaseConfirmationEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.893 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/appInAppInvoice.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.894 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/creation/otpAdmin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.895 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/bookPrice/_priceManager.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.896 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/oswalpublisher/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.896 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/oswalpublisher/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.897 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/oswalpublisher/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.897 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/oswalpublisher/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.897 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/deliveryCharges/manageDeliveryCharges.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.898 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/printbooks/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.898 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/printbooks/_categories.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.899 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/printbooks/printbooksmanagement.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.899 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/printbooks/_printSearch.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.899 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mail/activitiesUpdate.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.900 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mail/printbooks] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.900 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mail/sendInvite.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.900 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mail/printbooks/printbooksmanagement.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.901 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/pageManager.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.901 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.902 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/admin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.902 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/allWebsites.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.902 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/contactus.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.902 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.903 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/ibookso.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.903 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/customPageCreation.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.903 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.904 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/_otpOnlyLogin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.904 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.904 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/_ibooksoBanner.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.904 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/createNew.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.905 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/wileySignup.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.905 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/customPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.905 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/deleteAccount.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.906 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/cuetAcademics.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.906 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/loginPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.906 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/knimbus.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.906 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/cart.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.907 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/gptsir.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.907 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/accessCodeBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.907 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/addSite.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.908 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/listSites.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.908 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/privatelabel/aiStore.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.909 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/radianbooks/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.909 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/radianbooks/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.909 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/radianbooks/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.910 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/radianbooks/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.911 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_mobileSignup.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.911 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_plindex.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.911 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/notescreator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.912 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_wpfooter.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.912 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_mobileLogin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.912 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_topicscripts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.913 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/flpReportDownload.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.913 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/profile.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.913 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/home.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.913 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/success.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.914 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.914 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_mcq.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.914 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/signUp.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.915 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/privacy.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.915 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.915 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_mainheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.915 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_tandc.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.916 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_topicdisplay.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.916 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/findFriends.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.916 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/mylibrary.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.916 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_mcqalt.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.917 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/forgotpassword.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.917 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/quizcreator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.917 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/topic.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.917 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/groupdtl.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.918 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/facebook.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.918 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/quiz.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.918 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/signIn.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.919 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_mta.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.919 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/book.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.919 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_chaptersModal.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.920 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/tour.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.920 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_signIn.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.920 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_wsindex.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.921 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/recent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.921 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_topicinclude.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.921 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/test.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.921 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/aboutus.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.922 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.922 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/futureLearningProgram.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.922 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/groups.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.922 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/careers.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.923 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/termsandconditions.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.923 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_tof.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.923 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_fib.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.924 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_opp.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.924 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_signupNew.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.924 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/_pnavheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.924 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/renderContent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.925 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/renderMCQContent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.925 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/funlearn/deleteAccount.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.927 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instituteDashboard/doubts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.927 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instituteDashboard/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.927 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instituteDashboard/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.927 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instituteDashboard/reports.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.928 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instituteDashboard/classes.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.928 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instituteDashboard/students.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.928 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instituteDashboard/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.928 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instituteDashboard/teachers.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.929 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/groups/postDetail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.929 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/groups/groupReport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.930 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/groups/members.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.930 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/groups/groupDtl.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.930 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/groups/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.930 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/groups/_signIn.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.931 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/groups/groupCreate.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.931 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/groups/reported.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.931 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/groups/memberRequests.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.932 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderslatekids/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.932 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/toDo/toDoListAndUpdate.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.933 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/testgenerator/_testgen.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.933 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/testgenerator/monthlyQuiz.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.933 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepJoy/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.934 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepJoy/prepJoyNew.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.934 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepJoy/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.934 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepJoy/currentAffairsDaily.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.935 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prepJoy/currentAffairs.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.935 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/analytics/reportDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.935 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/analytics/leaderBoardForAdmins.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.936 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/support/getAllPayments.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.936 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/support/addUser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.936 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/support/findScratchCode.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.937 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wlibrary/library.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.937 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/validityExtensionAdmin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.938 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/cart.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.938 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/amazonorderconfirmation.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.938 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/_cartScripts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.939 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/_searchScripts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.939 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/manageShopSpecials.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.939 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/orderManagement.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.939 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/_shoppingCartLogic.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.940 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/blogEnglish.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.940 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/blogHindi.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.940 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/addGradeInfo.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.941 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/_videoCreator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.941 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsshop/_blogEnglishDisplay.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.942 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/messaging/messages.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.942 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/discussionBoardAdmin/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.942 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.943 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.943 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/_tandc.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.943 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/_wsabout.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.944 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/contact.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.944 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.944 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/eutkarshrefund.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.945 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.945 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.945 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/eutkarshprivacy.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.945 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/eutkarsh/eutkarshterms.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.946 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/intelligence/demo.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.946 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/intelligence/admin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.947 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/intelligence/gptContent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.947 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/intelligence/bookChapters.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.947 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/intelligence/chat.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.948 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_gradeSelector.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.948 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_addnotes.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.948 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/pdfReader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.949 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_bookdetailsTab.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.949 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/notescreator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.949 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_allSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.949 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_notesHighlightSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.950 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/mybooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.950 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/instructorLibrary.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.950 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/bookCreate.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.951 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_testgenModal.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.951 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_materialTabs.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.951 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/openPDF.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.951 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/demoHome.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.952 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_bookReviewModal.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.952 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_testgeneratorscripts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.952 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.953 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_answerMatchModal.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.953 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_additionalReferences.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.953 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/resetPasswordEmail.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.953 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/uploadTex.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.954 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_wsFooter.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.954 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_videoSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.954 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/wseditor.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.955 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.955 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_weblinksSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.955 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/orders.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.955 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/qandaCreator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.956 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_commonfooter_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.956 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/bannerManagement.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.956 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_loginChecker.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.957 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_revision.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.957 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_displayChapterDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.957 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/mylibrary.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.957 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/pubDesk.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.958 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/quizcreator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.958 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_addNotesScripts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.958 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_additionalReferenceSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.958 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_bookanalytics.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.959 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_bookChaptersTab.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.959 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/indexHome.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.959 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_quizSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.959 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/authorDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.960 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_bookReviews.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.960 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/book.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.960 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_flashCardSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.961 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_commonfooter.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.961 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/answerFixerInput.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.961 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/selfService.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.961 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/answerFixerProcessor.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.962 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_buyOrAdd.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.962 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/relatedVideosAdmin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.962 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/testgenerator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.962 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/answerMatch.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.963 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.963 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_discussform.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.963 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.964 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_uploadResourcesModal.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.964 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_gradeSelectorScript.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.964 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/pubSales.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.964 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_reviewrating.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.965 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_booksHolder.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.965 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/manageTabs.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.965 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/bookdtl.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.966 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_discforum.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.966 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_slider.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.966 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/independentContent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.966 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/quizcreatorbulkinput.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.967 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_buyChapters.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.967 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/quizcreatorbulk.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.967 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_booksHolderTemplate.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.967 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/videoplayer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.968 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/studySet.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.968 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/bookCreateNew.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.968 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/arihant.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.969 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/errorPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.969 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_wonderGoaStudySet.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.969 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_videoPlay.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.969 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/videoExplanation.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.970 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_readerView.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.970 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_qaSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.970 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/manageExams.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.970 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_readingSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.971 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/deleteAccount.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.971 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/_bookGPTTab.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.971 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderpublish/qandacreator.js] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.973 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/layouts/main.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.973 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/sales.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.974 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/profile.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.974 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/contentCreators.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.974 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.975 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/reports.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.975 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/users.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.975 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/classes.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.975 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/students.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.976 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/content.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.976 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/teachers.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.976 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/amazonLink.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.976 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/test.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.977 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/dashboard/fixBrokenResources.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.977 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/progress/progressReport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.978 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/libraryBooks/myLibrary.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.978 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/libraryBooks/_publishersList.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.979 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.979 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/help.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.979 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.979 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/privacy.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.980 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.980 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/_storeHolder.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.980 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/_commonfooter_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.980 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/contact.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.981 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.981 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.981 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.981 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/packages.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.982 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/feedback.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.982 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/termsCondition.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.982 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/library.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.982 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/etexts/terms.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.983 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/default/discussionBoard.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.983 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/default/chapterDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.984 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/default/jsonPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.984 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/default/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.984 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/default/remote.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.984 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/default/_showResult.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.985 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/myTracker.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.985 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.986 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/specimenCopyRequests.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.986 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/orders.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.986 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/favouriteMcqs.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.986 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/teachersNomineeForm.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.987 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/teachersPollResult.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.987 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/addUserAndBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.987 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/_ordersListInclude.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.987 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/editprofile.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.988 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/nominations.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.988 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/usermanagement/nomineeDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.988 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/publisherManagement/addPublisher.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.989 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/publisherManagement/publisherReport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.989 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/publisherManagement/publisher.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.989 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/reports/externalReportInput.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.990 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/reports/reports.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.990 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/reports/instituteReport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.990 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/reports/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.991 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/reports/pubSales.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.991 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/reports/getBulkUsersAddedReportInput.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.991 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/reports/scratchCardReport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.992 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resourceCreator/mergeQuizzes.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.992 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resourceCreator/mcqSorter.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.992 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resourceCreator/addNotes.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.992 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resourceCreator/sectionModifier.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.993 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resourceCreator/aiBookMigrate.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.993 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resourceCreator/findParentBook.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.993 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/resourceCreator/mergeMCQInput.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.994 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/addBulkUsersAndBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.995 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/formulaFixer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.995 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/bookInput.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.995 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/fileUploader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.995 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/_fileUploaderInclude.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.996 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/imageFixer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.996 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/quizExtractor.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.996 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/uploadMCQ.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.996 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/extractPDF.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.997 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/upload.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.997 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/htmlClassExtractor.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.997 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/wordUpload.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.997 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/exportmcqspage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:52.998 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/excel/uploadQA.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.001 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/_instructorResourcesContent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.002 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/faq.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.002 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/studentResourcesLocked.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.002 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/_userlogin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.002 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.003 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/ebook.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.003 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/privacy.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.003 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.004 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/instructorResources.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.004 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/instructorResourcesAdmin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.004 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/_studentResourcesLocked.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.004 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/instructorResourcesLocked.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.005 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/aboutus.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.005 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.005 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/register.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.005 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/_askAuthorModal.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.006 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/verification.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.006 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/disciplines.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.006 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/doris.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.007 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/_additionalStudentInfo.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.007 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sage/terms.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.008 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsLibrary/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.008 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsLibrary/myLibrary.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.008 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wsLibrary/accessCode.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.009 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/document/create.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.009 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/document/list.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.009 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/document/excelUpload.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.010 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/faq.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.010 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/store.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.010 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/ebouquetReviewerLogin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.011 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/virtualLibrary.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.011 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.011 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/_storeHolder.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.011 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/_commonfooter_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.012 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/information.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.012 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/contact.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.012 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/_accessCodeLogin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.013 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/_footer_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.013 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/howItWorks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.013 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/termsOfUse.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.013 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/_navheader_new.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.014 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.014 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/privacyPolicy.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.014 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/decision.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.015 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/requestDemo.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.015 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ebouquet/cookies.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.016 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/printOrderManagement/orderDetails.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.016 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mocktests/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.016 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mocktests/_relatedBooksAndLeaderboard.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.016 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mocktests/listExams.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.017 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mocktests/_topLevelTests.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.017 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mocktests/examPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.017 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/mocktests/_mockTestFaq.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.018 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/articles/showGradeBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.018 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/articles/getGradeBooks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.018 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/articles/showBlogPages.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.019 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/affiliationAdmin/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.019 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/affiliationAdmin/categoryManager.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.019 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/affiliationAdmin/searchResults.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.020 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/list.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.020 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/bookgpt.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.020 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_bookgptChatViewer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.021 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_bookgptPDFViewer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.021 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/showGptLogsForResId.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.021 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/showGptLogsForFeedback.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.022 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/showGptLogsForUser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.022 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/manualGpt.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.022 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_bookgptPDFViewerMobile.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.022 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_bookgptChatViewerMobile.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.023 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/myDriveReader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.023 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_driveChat.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.023 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_driveMobileReader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.023 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/pdfReader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.024 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_driveMobileChat.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.024 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_driveReader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.024 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_chatModule.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.025 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_codeRunner.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.025 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/prompt/_bookgptContentHolder.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.026 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/bookmark/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.026 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/digitalLibrary/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.026 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/digitalLibrary/showCategory.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.026 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/digitalLibrary/_categorySection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.027 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/digitalLibrary/adminIndex.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.027 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/autogpt/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.027 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/autogpt/promptTemplateManager.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.027 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/autogpt/duplicateFixer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.028 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/autogpt/kindleTemplate.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.028 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/autogpt/autogptTask.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.028 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/autogpt/test.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.028 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/autogpt/autoGPTAdmin.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.029 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/finance/salesReportByPublisher.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.029 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/finance/salesReportForAccounts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.029 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/partner/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.030 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/studyMaterial/_copilot.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.030 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/studyMaterial/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.030 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/webInteraction/manageLinks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.030 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/webInteraction/_aira.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.031 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/webInteraction/pageInteractions.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.031 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ibookgpt/howItWorks.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.031 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ibookgpt/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.031 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ibookgpt/_footer.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.031 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ibookgpt/contact.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.032 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ibookgpt/faq.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.032 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ibookgpt/_navheader.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.032 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ibookgpt/benefits.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.032 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ibookgpt/features.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.033 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/ibookgpt/printBookBundling.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.033 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/sdk/sdkIntegrationReport.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.033 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/listBatches.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.034 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/listUsersInBatch.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.034 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/addInstitute.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.034 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/editUser.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.034 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/getBatch.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.034 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/adminDashboard.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.035 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/listInstitutes.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.035 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/listBooksInBatch.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.035 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/assignUserToBatch.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.035 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/assignBookToBatch.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.035 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/editBatch.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.036 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/editCourse.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.036 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/createBatch.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.036 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/listCourses.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.036 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/createCourse.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.037 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/instManager/manageInstitutePrompts.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.037 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/promptLanguages/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.038 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/onlineTest/testQuestionAnalytics.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.038 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/onlineTest/blockStudents.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.038 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/onlineTest/listTests.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.038 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/onlineTest/testResults.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.039 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/onlineTest/viewQuestions.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.039 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/pdfExtractor/mcqExtractor.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.039 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/pdfExtractor/_api_modal.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.040 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/pdfExtractor/createSolution.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.040 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/pdfExtractor/mcqTranslator.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.040 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/contentdelivery/getPdfFile.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.041 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/contentdelivery/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.041 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/contentdelivery/cookieTest.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.041 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/questionPaper/createQuestionPaperPage.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.041 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/questionPaper/listQuestionPapers.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.042 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/questionPaper/viewQuestionPaper.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.042 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/questionPaper/addQuestionType.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.042 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/questionPaper/viewPattern.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.042 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/questionPaper/listPatterns.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.043 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/questionPaper/addSection.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.043 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/questionPaper/printQuestionPaper.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.043 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/aireport/dashboard.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.044 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/questionTypes/create.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.044 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/questionTypes/list.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.044 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/contentCreation/getSolvedPaperList.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.045 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/chat/index.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.045 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/chat/_rightContent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.045 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/chat/_leftContent.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.046 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/pdfExporter/sample.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.046 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/pdfExporter/chapterPdf.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.046 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/pdfExporter/bookTitle.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.046 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/pdfExporter/theoryChapter.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.047 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/theoryBooks/theoryBookInput.gsp] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.047 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/wonderslate/LoginFilters.groovy] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.048 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/spring/resources.groovy] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:42:53.053 WARNING [ws-startStop-1] org.apache.catalina.webresources.Cache.getResource Unable to add the resource at [/WEB-INF/classes/gsp/views.properties] to the cache for web application [] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
20-Aug-2025 04:43:03.530 INFO [ws-startStop-1] org.apache.jasper.servlet.TldScanner.scanJars At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
20-Aug-2025 04:43:05.954 INFO [ws-startStop-1] org.apache.catalina.core.ApplicationContext.log 2 Spring WebApplicationInitializers detected on classpath

Configuring Spring Security Core ...
... finished configuring Spring Security Core


Configuring Spring Security REST 2.0.0.M2...
... finished configuring Spring Security REST

	... with GORM support
20-Aug-2025 04:44:55.745 INFO [ws-startStop-1] org.apache.catalina.startup.HostConfig.deployDescriptor Deployment of configuration descriptor /u01/apache-tomcat-8.5.13/conf/Catalina/ws/ROOT.xml has finished in 138,870 ms
20-Aug-2025 04:44:55.751 INFO [ws-startStop-1] org.apache.catalina.startup.HostConfig.deployWAR Deploying web application archive /u01/apache-tomcat-8.5.13/webapps/wonderlive.war
20-Aug-2025 04:45:08.594 INFO [ws-startStop-1] org.apache.catalina.core.ApplicationContext.log 2 Spring WebApplicationInitializers detected on classpath

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::        (v2.2.2.RELEASE)

2025-08-20 04:45:09.947  INFO 2495079 --- [ ws-startStop-1] com.wonderslate.chat.ServletInitializer  : Starting ServletInitializer v0.0.1-SNAPSHOT on ip-172-31-30-27.us-west-2.compute.internal with PID 2495079 (/u01/apache-tomcat-8.5.13/webapps/wonderlive/WEB-INF/classes started by root in /u01/apache-tomcat-8.5.13/bin)
2025-08-20 04:45:09.955  INFO 2495079 --- [ ws-startStop-1] com.wonderslate.chat.ServletInitializer  : No active profile set, falling back to default profiles: default
20-Aug-2025 04:45:11.576 INFO [ws-startStop-1] org.apache.catalina.core.ApplicationContext.log Initializing Spring embedded WebApplicationContext
2025-08-20 04:45:11.583  INFO 2495079 --- [ ws-startStop-1] o.s.web.context.ContextLoader            : Root WebApplicationContext: initialization completed in 1511 ms
2025-08-20 04:45:12.028  INFO 2495079 --- [ ws-startStop-1] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService 'clientInboundChannelExecutor'
2025-08-20 04:45:12.034  INFO 2495079 --- [ ws-startStop-1] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService 'clientOutboundChannelExecutor'
2025-08-20 04:45:12.079  INFO 2495079 --- [ ws-startStop-1] o.s.s.c.ThreadPoolTaskScheduler          : Initializing ExecutorService 'messageBrokerTaskScheduler'
2025-08-20 04:45:12.153  INFO 2495079 --- [ ws-startStop-1] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService 'brokerChannelExecutor'
2025-08-20 04:45:13.034  WARN 2495079 --- [ ws-startStop-1] o.s.b.a.f.FreeMarkerAutoConfiguration    : Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-08-20 04:45:13.333  INFO 2495079 --- [ ws-startStop-1] o.s.m.s.b.SimpleBrokerMessageHandler     : Starting...
2025-08-20 04:45:13.333  INFO 2495079 --- [ ws-startStop-1] o.s.m.s.b.SimpleBrokerMessageHandler     : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [DefaultSubscriptionRegistry[cache[0 destination(s)], registry[0 sessions]]]]
2025-08-20 04:45:13.334  INFO 2495079 --- [ ws-startStop-1] o.s.m.s.b.SimpleBrokerMessageHandler     : Started.
2025-08-20 04:45:13.355  INFO 2495079 --- [ ws-startStop-1] com.wonderslate.chat.ServletInitializer  : Started ServletInitializer in 4.536 seconds (JVM running for 158.322)
20-Aug-2025 04:45:13.371 INFO [ws-startStop-1] org.apache.catalina.startup.HostConfig.deployWAR Deployment of web application archive /u01/apache-tomcat-8.5.13/webapps/wonderlive.war has finished in 17,620 ms
20-Aug-2025 04:45:13.372 INFO [ws-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory /u01/apache-tomcat-8.5.13/webapps/.well-known
20-Aug-2025 04:45:13.391 INFO [ws-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory /u01/apache-tomcat-8.5.13/webapps/.well-known has finished in 19 ms
20-Aug-2025 04:45:13.391 INFO [ws-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory /u01/apache-tomcat-8.5.13/webapps/tools
20-Aug-2025 04:45:13.411 INFO [ws-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory /u01/apache-tomcat-8.5.13/webapps/tools has finished in 20 ms
20-Aug-2025 04:45:13.430 INFO [main] org.apache.coyote.AbstractProtocol.start Starting ProtocolHandler ["ajp-nio-8009"]
20-Aug-2025 04:45:13.446 INFO [main] org.apache.catalina.startup.Catalina.start Server startup in 156651 ms
Executing SQL: select scad.book_id bookId,bm.cover_image,bm.title,(select name from publishers where id=bm.publisher_id) publisherName,bm.price,bm.tests_price,scad.book_type,bm.upgrade_price,scad.subscription_id,scad.subs_duration,scad.subs_starting_book_id,bm.book_type baseBookType,bm.publisher_id  from  shopping_cart_active_dtl scad ,books_mst bm where  bm.id=scad.book_id and    scad.username='16ECBA2A79F3B5C55418AFA4159EC0DEB_temp' and scad.site_id='1' order by scad.id Desc 
20-Aug-2025 04:45:15.613 INFO [ajp-nio-8009-exec-4] org.apache.tomcat.util.http.parser.Cookie.logInvalidHeader A cookie header was received [Aug 20 2025 09:09:46 GMT+0530 (India Standard Time); _ga_2Y9D9ELSN2=GS2.1.s1755661176$o166$g1$t1755663362$j60$l0$h0; pomodoroCurrentDuration=801; daysDuration=387] that contained an invalid cookie. That cookie will be ignored.Note: further occurrences of this error will be logged at DEBUG level.
Executing SQL:  SELECT bud.batch_id, im.id institute_id, im.name,im.publisher_id publisherId,im.logo,im.edu_wonder,cbd.name batchName,im.full_library_view,cbd.syllabus,cbd.grade,im.level,im.syllabus instituteSyllabus,im.drive_for_instructor,drive_for_student,ra_for_instructor,ra_for_student,show_reference_section,bud.user_type,im.enable_test,im.enable_analytics,im.enable_question_paper  FROM wsuser.batch_user_dtl bud, wsuser.course_batches_dtl cbd, wsuser.institute_mst im  where im.site_id=1 and bud.username='<EMAIL>'  and cbd.id=bud.batch_id and im.id=cbd.conducted_by  and cbd.status='active' and (cbd.start_date <= SYSDATE() or cbd.start_date is null)  union  select cbd.id batch_id, im.id institute_id, im.name,im.publisher_id publisherId,im.logo,im.edu_wonder,cbd.name batchName,im.full_library_view,cbd.syllabus,cbd.grade,im.level,im.syllabus instituteSyllabus,im.drive_for_instructor,drive_for_student,ra_for_instructor,ra_for_student,show_reference_section,'false' user_type,im.enable_test,im.enable_analytics,im.enable_question_paper from wsuser.institute_mst im, wsuser.course_batches_dtl cbd, wsuser.institute_ip_address iia  where iia.ip_address= '**************' and im.id =iia.institute_id and  im.id=cbd.conducted_by  and cbd.status='active' and (cbd.start_date <= SYSDATE() or cbd.start_date is null) and im.site_id=1
[batchId:200, id:143, name:New Test Institute, publisherId:169, batchName:Default, fullLibraryView:true, syllabus:null, grade:null, logo:1.png, isEduWonder:null, isInstructor:false, level:null, instituteSyllabus:null, driveForInstructor:null, driveForStudent:null, raForInstructor:null, raForStudent:null, showReferenceSection:null, enableTest:null, enableAnalytics:null, enableQuestionPaper:null, userType:null]
[batchId:273, id:213, name:R V College, publisherId:109, batchName:Default, fullLibraryView:null, syllabus:null, grade:null, logo:null, isEduWonder:null, isInstructor:false, level:null, instituteSyllabus:null, driveForInstructor:null, driveForStudent:null, raForInstructor:null, raForStudent:null, showReferenceSection:null, enableTest:null, enableAnalytics:null, enableQuestionPaper:null, userType:null]
[batchId:342, id:270, name:Pavithra New Institute July, publisherId:129, batchName:Default, fullLibraryView:true, syllabus:null, grade:null, logo:null, isEduWonder:null, isInstructor:false, level:null, instituteSyllabus:null, driveForInstructor:null, driveForStudent:null, raForInstructor:null, raForStudent:null, showReferenceSection:null, enableTest:null, enableAnalytics:null, enableQuestionPaper:null, userType:null]
[batchId:401, id:312, name:New Institute with free and paid book non IP, publisherId:170, batchName:Default, fullLibraryView:true, syllabus:null, grade:null, logo:null, isEduWonder:false, isInstructor:false, level:, instituteSyllabus:, driveForInstructor:null, driveForStudent:null, raForInstructor:null, raForStudent:null, showReferenceSection:null, enableTest:null, enableAnalytics:null, enableQuestionPaper:null, userType:null]
[batchId:472, id:368, name:New Eduwonder full library 14th, publisherId:206, batchName:Default, fullLibraryView:true, syllabus:null, grade:null, logo:E-vidyaimage.png, isEduWonder:true, isInstructor:false, level:, instituteSyllabus:, driveForInstructor:null, driveForStudent:null, raForInstructor:null, raForStudent:null, showReferenceSection:null, enableTest:null, enableAnalytics:null, enableQuestionPaper:null, userType:null]
[batchId:473, id:368, name:Class 10, publisherId:206, batchName:Class 10, fullLibraryView:true, syllabus:null, grade:null, logo:E-vidyaimage.png, isEduWonder:true, isInstructor:false, level:, instituteSyllabus:, driveForInstructor:null, driveForStudent:null, raForInstructor:null, raForStudent:null, showReferenceSection:null, enableTest:null, enableAnalytics:null, enableQuestionPaper:null, userType:null]
Executing SQL: select * from quiz_rec_dtl where quiz_rec_id=5487 order by id
Executing SQL: SELECT * FROM objective_mst   where id in (148203,148204,148205,148206,148207,148208,148209,148210,148211,148212,148213,148214,148215,148216,148217,148218,148219,148220,148221,148222,148223,148224,148225,148226,148227,148228,148229,148230,148231,148232,148233,148234,148235,148236,148237,148238,148239,148240,148241,148242) 
2025-08-20 04:45:39.812 ERROR --- [nio-8009-exec-5] StackTrace                               : Full Stack Trace:

groovy.lang.MissingPropertyException: No such property: quiz for class: com.wonderslate.data.PrepjoyService
	at org.codehaus.groovy.runtime.ScriptBytecodeAdapter.unwrap(ScriptBytecodeAdapter.java:53)
	at org.codehaus.groovy.runtime.callsite.GetEffectivePogoPropertySite.getProperty(GetEffectivePogoPropertySite.java:87)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callGroovyObjectGetProperty(AbstractCallSite.java:307)
	at com.wonderslate.data.PrepjoyService.$tt__getFullQuizDetails(PrepjoyService.groovy:2212)
	at com.wonderslate.data.PrepjoyService$_getFullQuizDetails_closure23.doCall(PrepjoyService.groovy)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:93)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:325)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:294)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1027)
	at groovy.lang.Closure.call(Closure.java:414)
	at groovy.lang.Closure.call(Closure.java:430)
	at grails.transaction.GrailsTransactionTemplate$2.doInTransaction(GrailsTransactionTemplate.groovy:96)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:133)
	at grails.transaction.GrailsTransactionTemplate.execute(GrailsTransactionTemplate.groovy:93)
	at com.wonderslate.data.PrepjoyService.getFullQuizDetails(PrepjoyService.groovy)
	at com.wonderslate.data.PrepjoyService$getFullQuizDetails.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:48)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:113)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at com.wonderslate.games.PrepjoyController.$tt__getFullQuizDetails(PrepjoyController.groovy:530)
	at com.wonderslate.games.PrepjoyController$_getFullQuizDetails_closure27.doCall(PrepjoyController.groovy)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:93)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:325)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:294)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1027)
	at groovy.lang.Closure.call(Closure.java:414)
	at groovy.lang.Closure.call(Closure.java:430)
	at grails.transaction.GrailsTransactionTemplate$2.doInTransaction(GrailsTransactionTemplate.groovy:96)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:133)
	at grails.transaction.GrailsTransactionTemplate.execute(GrailsTransactionTemplate.groovy:93)
	at com.wonderslate.games.PrepjoyController.getFullQuizDetails(PrepjoyController.groovy)
	at org.grails.core.DefaultGrailsControllerClass$MethodHandleInvoker.invoke(DefaultGrailsControllerClass.java:223)
	at org.grails.core.DefaultGrailsControllerClass.invoke(DefaultGrailsControllerClass.java:188)
	at org.grails.web.mapping.mvc.UrlMappingsInfoHandlerAdapter.handle(UrlMappingsInfoHandlerAdapter.groovy:90)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:963)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:897)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:970)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:872)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:661)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:846)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:742)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:230)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at javax.servlet.FilterChain$doFilter$0.call(Unknown Source)
	at grails.plugin.springsecurity.rest.RestLogoutFilter.doFilter(RestLogoutFilter.groovy:80)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.springframework.boot.web.filter.ApplicationContextHeaderFilter.doFilterInternal(ApplicationContextHeaderFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.springframework.boot.actuate.trace.WebRequestTraceFilter.doFilterInternal(WebRequestTraceFilter.java:105)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:317)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:127)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:91)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at grails.plugin.springsecurity.web.UpdateRequestContextHolderExceptionTranslationFilter.doFilter(UpdateRequestContextHolderExceptionTranslationFilter.groovy:64)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at javax.servlet.FilterChain$doFilter$0.call(Unknown Source)
	at grails.plugin.springsecurity.rest.RestTokenValidationFilter.processFilterChain(RestTokenValidationFilter.groovy:122)
	at sun.reflect.GeneratedMethodAccessor667.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite$PogoCachedMethodSiteNoUnwrapNoCoerce.invoke(PogoMetaMethodSite.java:210)
	at org.codehaus.groovy.runtime.callsite.PogoMetaMethodSite.callCurrent(PogoMetaMethodSite.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callCurrent(AbstractCallSite.java:190)
	at grails.plugin.springsecurity.rest.RestTokenValidationFilter.doFilter(RestTokenValidationFilter.groovy:90)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at grails.plugin.springsecurity.web.filter.GrailsAnonymousAuthenticationFilter.doFilter(GrailsAnonymousAuthenticationFilter.groovy:53)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.authentication.rememberme.RememberMeAuthenticationFilter.doFilter(RememberMeAuthenticationFilter.java:158)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:169)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at javax.servlet.FilterChain$doFilter$0.call(Unknown Source)
	at grails.plugin.springsecurity.rest.RestAuthenticationFilter.doFilter(RestAuthenticationFilter.groovy:143)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at grails.plugin.springsecurity.web.authentication.logout.MutableLogoutFilter.doFilter(MutableLogoutFilter.groovy:62)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at grails.plugin.springsecurity.web.SecurityRequestHolderFilter.doFilter(SecurityRequestHolderFilter.groovy:58)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:214)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:177)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.grails.web.servlet.mvc.GrailsWebRequestFilter.doFilterInternal(GrailsWebRequestFilter.java:77)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.grails.web.filters.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:67)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:197)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.springframework.boot.actuate.autoconfigure.MetricsFilter.doFilterInternal(MetricsFilter.java:106)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.springframework.boot.web.support.ErrorPageFilter.doFilter(ErrorPageFilter.java:117)
	at org.springframework.boot.web.support.ErrorPageFilter.access$000(ErrorPageFilter.java:61)
	at org.springframework.boot.web.support.ErrorPageFilter$1.doFilterInternal(ErrorPageFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.boot.web.support.ErrorPageFilter.doFilter(ErrorPageFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:198)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:478)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:140)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:80)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:87)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:341)
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:486)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:861)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1455)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)

2025-08-20 04:45:39.818 ERROR --- [nio-8009-exec-5] StackTrace                               : Full Stack Trace:

groovy.lang.MissingPropertyException: No such property: quiz for class: com.wonderslate.data.PrepjoyService
	at com.wonderslate.data.PrepjoyService.$tt__getFullQuizDetails(PrepjoyService.groovy:2212)
	at grails.transaction.GrailsTransactionTemplate$2.doInTransaction(GrailsTransactionTemplate.groovy:96)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:133)
	at grails.transaction.GrailsTransactionTemplate.execute(GrailsTransactionTemplate.groovy:93)
	at com.wonderslate.games.PrepjoyController.$tt__getFullQuizDetails(PrepjoyController.groovy:530)
	at grails.transaction.GrailsTransactionTemplate$2.doInTransaction(GrailsTransactionTemplate.groovy:96)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:133)
	at grails.transaction.GrailsTransactionTemplate.execute(GrailsTransactionTemplate.groovy:93)
	at org.grails.core.DefaultGrailsControllerClass$MethodHandleInvoker.invoke(DefaultGrailsControllerClass.java:223)
	at org.grails.core.DefaultGrailsControllerClass.invoke(DefaultGrailsControllerClass.java:188)
	at org.grails.web.mapping.mvc.UrlMappingsInfoHandlerAdapter.handle(UrlMappingsInfoHandlerAdapter.groovy:90)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:963)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:897)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:970)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:872)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:846)
	at grails.plugin.springsecurity.rest.RestLogoutFilter.doFilter(RestLogoutFilter.groovy:80)
	at org.springframework.boot.web.filter.ApplicationContextHeaderFilter.doFilterInternal(ApplicationContextHeaderFilter.java:55)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:317)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:127)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:91)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at grails.plugin.springsecurity.web.UpdateRequestContextHolderExceptionTranslationFilter.doFilter(UpdateRequestContextHolderExceptionTranslationFilter.groovy:64)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at grails.plugin.springsecurity.rest.RestTokenValidationFilter.processFilterChain(RestTokenValidationFilter.groovy:122)
	at grails.plugin.springsecurity.rest.RestTokenValidationFilter.doFilter(RestTokenValidationFilter.groovy:90)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at grails.plugin.springsecurity.web.filter.GrailsAnonymousAuthenticationFilter.doFilter(GrailsAnonymousAuthenticationFilter.groovy:53)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.authentication.rememberme.RememberMeAuthenticationFilter.doFilter(RememberMeAuthenticationFilter.java:158)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:169)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at grails.plugin.springsecurity.rest.RestAuthenticationFilter.doFilter(RestAuthenticationFilter.groovy:143)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at grails.plugin.springsecurity.web.authentication.logout.MutableLogoutFilter.doFilter(MutableLogoutFilter.groovy:62)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at grails.plugin.springsecurity.web.SecurityRequestHolderFilter.doFilter(SecurityRequestHolderFilter.groovy:58)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:214)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:177)
	at org.grails.web.servlet.mvc.GrailsWebRequestFilter.doFilterInternal(GrailsWebRequestFilter.java:77)
	at org.grails.web.filters.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:67)
	at org.springframework.boot.web.support.ErrorPageFilter.doFilter(ErrorPageFilter.java:117)
	at org.springframework.boot.web.support.ErrorPageFilter.access$000(ErrorPageFilter.java:61)
	at org.springframework.boot.web.support.ErrorPageFilter$1.doFilterInternal(ErrorPageFilter.java:92)
	at org.springframework.boot.web.support.ErrorPageFilter.doFilter(ErrorPageFilter.java:110)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

2025-08-20 04:45:39.886 ERROR --- [nio-8009-exec-5] o.g.web.errors.GrailsExceptionResolver   : MissingPropertyException occurred when processing request: [POST] /prepjoy/getFullQuizDetails
No such property: quiz for class: com.wonderslate.data.PrepjoyService. Stacktrace follows:

groovy.lang.MissingPropertyException: No such property: quiz for class: com.wonderslate.data.PrepjoyService
	at com.wonderslate.data.PrepjoyService.$tt__getFullQuizDetails(PrepjoyService.groovy:2212)
	at grails.transaction.GrailsTransactionTemplate$2.doInTransaction(GrailsTransactionTemplate.groovy:96)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:133)
	at grails.transaction.GrailsTransactionTemplate.execute(GrailsTransactionTemplate.groovy:93)
	at com.wonderslate.games.PrepjoyController.$tt__getFullQuizDetails(PrepjoyController.groovy:530)
	at grails.transaction.GrailsTransactionTemplate$2.doInTransaction(GrailsTransactionTemplate.groovy:96)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:133)
	at grails.transaction.GrailsTransactionTemplate.execute(GrailsTransactionTemplate.groovy:93)
	at org.grails.core.DefaultGrailsControllerClass$MethodHandleInvoker.invoke(DefaultGrailsControllerClass.java:223)
	at org.grails.core.DefaultGrailsControllerClass.invoke(DefaultGrailsControllerClass.java:188)
	at org.grails.web.mapping.mvc.UrlMappingsInfoHandlerAdapter.handle(UrlMappingsInfoHandlerAdapter.groovy:90)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:963)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:897)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:970)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:872)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:846)
	at grails.plugin.springsecurity.rest.RestLogoutFilter.doFilter(RestLogoutFilter.groovy:80)
	at org.springframework.boot.web.filter.ApplicationContextHeaderFilter.doFilterInternal(ApplicationContextHeaderFilter.java:55)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:317)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:127)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:91)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at grails.plugin.springsecurity.web.UpdateRequestContextHolderExceptionTranslationFilter.doFilter(UpdateRequestContextHolderExceptionTranslationFilter.groovy:64)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at grails.plugin.springsecurity.rest.RestTokenValidationFilter.processFilterChain(RestTokenValidationFilter.groovy:122)
	at grails.plugin.springsecurity.rest.RestTokenValidationFilter.doFilter(RestTokenValidationFilter.groovy:90)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at grails.plugin.springsecurity.web.filter.GrailsAnonymousAuthenticationFilter.doFilter(GrailsAnonymousAuthenticationFilter.groovy:53)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.authentication.rememberme.RememberMeAuthenticationFilter.doFilter(RememberMeAuthenticationFilter.java:158)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:169)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at grails.plugin.springsecurity.rest.RestAuthenticationFilter.doFilter(RestAuthenticationFilter.groovy:143)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:200)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at grails.plugin.springsecurity.web.authentication.logout.MutableLogoutFilter.doFilter(MutableLogoutFilter.groovy:62)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at grails.plugin.springsecurity.web.SecurityRequestHolderFilter.doFilter(SecurityRequestHolderFilter.groovy:58)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:331)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:214)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:177)
	at org.grails.web.servlet.mvc.GrailsWebRequestFilter.doFilterInternal(GrailsWebRequestFilter.java:77)
	at org.grails.web.filters.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:67)
	at org.springframework.boot.web.support.ErrorPageFilter.doFilter(ErrorPageFilter.java:117)
	at org.springframework.boot.web.support.ErrorPageFilter.access$000(ErrorPageFilter.java:61)
	at org.springframework.boot.web.support.ErrorPageFilter$1.doFilterInternal(ErrorPageFilter.java:92)
	at org.springframework.boot.web.support.ErrorPageFilter.doFilter(ErrorPageFilter.java:110)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

Executing SQL: select scad.book_id bookId,bm.cover_image,bm.title,(select name from publishers where id=bm.publisher_id) publisherName,bm.price,bm.tests_price,scad.book_type,bm.upgrade_price,scad.subscription_id,scad.subs_duration,scad.subs_starting_book_id,bm.book_type baseBookType,bm.publisher_id  from  shopping_cart_active_dtl scad ,books_mst bm where  bm.id=scad.book_id and    scad.username='15F039CDE7944ED95D6A4AF0C947BE174_temp' and scad.site_id='1' order by scad.id Desc 
Executing SQL: select scad.book_id bookId,bm.cover_image,bm.title,(select name from publishers where id=bm.publisher_id) publisherName,bm.price,bm.tests_price,scad.book_type,bm.upgrade_price,scad.subscription_id,scad.subs_duration,scad.subs_starting_book_id,bm.book_type baseBookType,bm.publisher_id  from  shopping_cart_active_dtl scad ,books_mst bm where  bm.id=scad.book_id and    scad.username='12C6B897A56980BCF1267A043F4FE271A_temp' and scad.site_id='1' order by scad.id Desc 
is this compiled or not
Executing SQL: select p.id publisherId,p.name publisher from books_mst bk, books_tag_dtl btd, publishers p  where bk.id=btd.book_id and bk.site_id in(0,1,2,3,8,10,11,13,14,15,17,18,26,27,28,29,30,31,32,33,35,36,37,38,39,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,80,82,88,90,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,114,115,129,160) and bk.status in ('free','published') and p.id=bk.publisher_id  and btd.level in ('College') and bk.status in ('free','published')  group by p.id,p.name order by p.name 
Executing SQL: select level,syllabus,grade,subject from books_tag_dtl where id=-99
Executing SQL: select bk.id,bk.title,bk.cover_image,bk.language,bk.book_type,GROUP_CONCAT(btd.subject) as subject,bk.buylink1,bk.listprice,bk.price,p.name, p.id publisherId, GROUP_CONCAT(btd.grade) as grade,GROUP_CONCAT(btd.syllabus) as syllabus,GROUP_CONCAT(btd.level) as level,bk.medium,coalesce(bk.validity_days,' ') as validityDays, CASE WHEN price > 0 THEN 1 ELSE 0 end as free,bk.status,bk.allow_subscription,bk.has_quiz,bk.tests_price,bk.tests_listprice,bk.upgrade_price,bk.isbn,bk.test_type_book  from books_mst bk,books_tag_dtl btd, publishers p,levels_mst lm  where bk.id=btd.book_id and lm.site_id=1 and lm.name=btd.level and  bk.site_id in(0,1,2,3,8,10,11,13,14,15,17,18,26,27,28,29,30,31,32,33,35,36,37,38,39,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,80,82,88,90,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,114,115,129,160)  and p.id=bk.publisher_id  and btd.level in ('College') and bk.status in ('free','published')  GROUP BY bk.id , bk.title , bk.cover_image , bk.language , bk.book_type , bk.buylink1 , bk.listprice , bk.price , p.name , p.id,last_sold order by last_sold desc, date_published  desc limit 0,20
Executing SQL: select scad.book_id bookId,bm.cover_image,bm.title,(select name from publishers where id=bm.publisher_id) publisherName,bm.price,bm.tests_price,scad.book_type,bm.upgrade_price,scad.subscription_id,scad.subs_duration,scad.subs_starting_book_id,bm.book_type baseBookType,bm.publisher_id  from  shopping_cart_active_dtl scad ,books_mst bm where  bm.id=scad.book_id and    scad.username='16CAC686CF7937AB67E5BB35DE1A95FB4_temp' and scad.site_id='1' order by scad.id Desc 
Executing SQL: select scad.book_id bookId,bm.cover_image,bm.title,(select name from publishers where id=bm.publisher_id) publisherName,bm.price,bm.tests_price,scad.book_type,bm.upgrade_price,scad.subscription_id,scad.subs_duration,scad.subs_starting_book_id,bm.book_type baseBookType,bm.publisher_id  from  shopping_cart_active_dtl scad ,books_mst bm where  bm.id=scad.book_id and    scad.username='16AF17D5929375A54CA0D56B462C3FFCF_temp' and scad.site_id='1' order by scad.id Desc 
Executing SQL: select count(medal) medalcount,medal from user_medals where username='<EMAIL>' group by medal
Executing SQL: SELECT sum(points) totalPoints FROM wsuser.quiz_rec_mst where date(DATE_ADD(date_created, INTERVAL '5:30' HOUR_MINUTE)) = date('2025-08-20') and username='<EMAIL>'
Executing SQL: SELECT sum(duration) FROM user_time_log where DATE(logged_date) > date_add(CURDATE() , INTERVAL -7 day) and username='<EMAIL>'
Executing SQL: SELECT sum(qrd.user_time) FROM quiz_rec_dtl qrd,quiz_rec_mst qrm where DATE(qrm.date_created) > date_add(CURDATE() , INTERVAL -7 day) and qrm.username='<EMAIL>' and qrd.quiz_rec_id=qrm.id
sql=select  sum(qrm.correct_answers) correct,sum(qrm.incorrect_answers) incorrect,sum(qrm.skipped) skipped
from wsuser.quiz_rec_mst qrm
where qrm.username='<EMAIL>' and DATE(qrm.date_created) > date_add(CURDATE() , INTERVAL - 7 day)

Executing SQL:  SELECT bud.batch_id, im.id institute_id, im.name,im.publisher_id publisherId,im.logo,im.edu_wonder,cbd.name batchName,im.full_library_view,cbd.syllabus,cbd.grade,im.level,im.syllabus instituteSyllabus,im.drive_for_instructor,drive_for_student,ra_for_instructor,ra_for_student,show_reference_section,bud.user_type,im.enable_test,im.enable_analytics,im.enable_question_paper  FROM wsuser.batch_user_dtl bud, wsuser.course_batches_dtl cbd, wsuser.institute_mst im  where im.site_id=1 and bud.username='<EMAIL>'  and cbd.id=bud.batch_id and im.id=cbd.conducted_by  and cbd.status='active' and (cbd.start_date <= SYSDATE() or cbd.start_date is null)  union  select cbd.id batch_id, im.id institute_id, im.name,im.publisher_id publisherId,im.logo,im.edu_wonder,cbd.name batchName,im.full_library_view,cbd.syllabus,cbd.grade,im.level,im.syllabus instituteSyllabus,im.drive_for_instructor,drive_for_student,ra_for_instructor,ra_for_student,show_reference_section,'false' user_type,im.enable_test,im.enable_analytics,im.enable_question_paper from wsuser.institute_mst im, wsuser.course_batches_dtl cbd, wsuser.institute_ip_address iia  where iia.ip_address= '**************' and im.id =iia.institute_id and  im.id=cbd.conducted_by  and cbd.status='active' and (cbd.start_date <= SYSDATE() or cbd.start_date is null) and im.site_id=1
Executing SQL: SELECT max(id) maxId, book_id FROM wslog.books_view_dtl where username='<EMAIL>' group by book_id order by maxId desc limit 5
2025-08-20 04:46:12.151  INFO 2495079 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats    : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
Executing SQL: SELECT count(distinct(book_id)) noOfBooks FROM books_permission where username='<EMAIL>'
