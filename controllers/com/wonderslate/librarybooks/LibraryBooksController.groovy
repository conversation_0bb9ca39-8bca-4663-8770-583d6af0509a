package com.wonderslate.librarybooks

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.wonderslate.WsLibrary.WsLibraryService
import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.GptLogService
import com.wonderslate.data.SiteMst
import com.wonderslate.data.UtilService
import com.wonderslate.institute.BatchUserDtl
import com.wonderslate.publiclibrary.LibraryCategory
import com.wonderslate.publish.BooksPermission
import com.wonderslate.shop.WsshopService
import com.wonderslate.sqlutil.SafeSql
import com.wonderslate.usermanagement.User
import com.wonderslate.usermanagement.UserManagementService
import grails.converters.JSON
import grails.plugin.springsecurity.SpringSecurityService
import grails.plugin.springsecurity.annotation.Secured
import grails.plugins.mail.MailService
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import org.grails.web.util.WebUtils

import javax.servlet.http.Cookie
import java.text.DateFormat
import java.text.SimpleDateFormat

@Transactional
class LibraryBooksController {
    UserManagementService userManagementService
    SpringSecurityService springSecurityService
    def redisService
    DataProviderService dataProviderService
    LibraryBooksService libraryBooksService
    UtilService utilService
    WsshopService wsshopService
    WsLibraryService wsLibraryService
    GptLogService gptLogService
    MailService mailService

    @Transactional
    def getUsersInstituteBooks() {
        def json
        def institutebooks
        def pageNo = params.pageNo
        String filter = params.filter
        def validityDateOfUser=null
        String validityOver="false"
        def enforceValidity="false"

        if(springSecurityService.currentUser!=null&&springSecurityService.currentUser.username!=null&&params.batchId!=null&&!"".equals(params.batchId)){
            BatchUserDtl batchUserDtl = BatchUserDtl.findByUsernameAndBatchId(springSecurityService.currentUser.username,new Integer(params.batchId))
            if(batchUserDtl!=null&&batchUserDtl.validityDate != null && !batchUserDtl.validityDate.equals("")) {
                validityDateOfUser = (new SimpleDateFormat("dd-MM-yyyy")).format(batchUserDtl.validityDate)
                def currentDate = new Date()
                // this is to add one more date to current date, to include the current date if the validity is today
                currentDate.plus(1)
                def givenDateObj = batchUserDtl.validityDate

                if (currentDate.after(givenDateObj)) {
                    validityOver="true"
                }
            }
        }

        if(filter.equals("true")){
            institutebooks= libraryBooksService.instituteLibraryFilters(new Integer(pageNo),params)
            json = [books: institutebooks.get("books"), 'status': institutebooks.get("books") ? "OK" : "Nothing present", totalBooks:institutebooks.get("count"), count:institutebooks.get("count"),
                    validityDateOfUser:validityDateOfUser,validityOver:validityOver,searchSuccessful:institutebooks.get("searchSuccessful")]
        }else {
            if(pageNo==null) pageNo="1"
            if (redisService.("instituteLibraryBooklist_" + params.batchId + "_page_"+pageNo) == null) libraryBooksService.getInstituteBooksPagination(params.batchId,Integer.parseInt(pageNo))
            institutebooks = redisService.("instituteLibraryBooklist_" + params.batchId + "_page_" + pageNo)

            json = [books: institutebooks, 'status': institutebooks ? "OK" : "Nothing present", totalBooks: redisService.("instituteLibrary_" + params.batchId + "_totalBooks"), count: redisService.("instituteLibrary_" + params.batchId + "_totalBooks"),
                    validityDateOfUser:validityDateOfUser,validityOver:validityOver]
        }
        render json as JSON
    }



    @Transactional
    def getUsersBooks() {
        def json
        if (springSecurityService.currentUser != null) {
            String username = springSecurityService.currentUser.username;
            int paidTokenCount = 0
            User user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
            if(user.chatTokensBalance!=null) {
                paidTokenCount = user.chatTokensBalance.intValue()
            }

            // Check if filtering is requested
            if (params.filter && params.filter == 'true') {
                // Use filtered results
                def pageNo = params.pageNo ? Integer.parseInt(params.pageNo) : 1
                def filteredResult = libraryBooksService.userBooksFilters(pageNo, params, username)
                def userBookslist = new JsonSlurper().parseText(filteredResult.books)

                if (redisService.("lastReadBooks_" + username) == null) {
                    dataProviderService.getLastReadBooks(username)
                }

                json = [books: userBookslist, 'lastReadBooks': redisService.("lastReadBooks_" + username), 'status': userBookslist ? "OK" : "Nothing present", count: filteredResult.count, paidTokenCount:paidTokenCount]
            } else {
                // Use original cached results
                def pageNo = params.pageNo
                def userBookslist
                if (redisService.("userShelfBooks_" + username) == null || redisService.("userShelfBooks_" + username) == "null") libraryBooksService.userShelfBooks(username)
                if (pageNo != null) {
                    userBookslist = new JsonSlurper().parseText(redisService.("userShelfBooks_" + username + "_page_" + pageNo))
                } else {
                    userBookslist = new JsonSlurper().parseText(redisService.("userShelfBooksAll_" + username))
                }
                if (redisService.("lastReadBooks_" + username) == null) {
                    dataProviderService.getLastReadBooks(username)
                }
                json = [books: userBookslist, 'lastReadBooks': redisService.("lastReadBooks_" + username), 'status': userBookslist ? "OK" : "Nothing present", count: redisService.("userShelfBooks_" + username + "_totalBooks"), paidTokenCount:paidTokenCount]
            }
        } else {
            json = [books: null]
        }
        render json as JSON

    }

    @Transactional
    def getUsersLastReadBooks(){
        def json
        if (springSecurityService.currentUser != null) {
            String username = springSecurityService.currentUser.username
            if (redisService.("lastReadBooks_" + username) == null) {
                dataProviderService.getLastReadBooks(username)
            }
            json = ['lastReadBooks': redisService.("lastReadBooks_" + username)]
        } else {
            json = ['lastReadBooks': null]
        }
        render json as JSON
    }

    @Transactional
    def getInstituteBooksTags(){
        if( redisService.("batchBooksFilters_"+params.batchId)==null){
            libraryBooksService.getInstituteBooksTags(params.batchId)
        }
        def json = ["booksTags":redisService.("batchBooksFilters_"+params.batchId)]
        render json as JSON
    }

    @Transactional
    def getUserBooksTags(){
        if (springSecurityService.currentUser != null) {
            String username = springSecurityService.currentUser.username;
            if( redisService.("userBooksFilters_"+username)==null){
                libraryBooksService.getUserBooksTags(username)
            }
            def json = ["booksTags":redisService.("userBooksFilters_"+username)]
            render json as JSON
        } else {
            def json = ["booksTags":null]
            render json as JSON
        }
    }

    def Integer getSiteId(request){
        Integer siteId = new Integer(1)

        if(session["siteId"]!=null){
            siteId = (Integer)session["siteId"]
        } else {
            def jsonObj = request.JSON

            if(jsonObj.siteId!=null) {
                siteId = new Integer(jsonObj.siteId)
            } else if(params.siteId!=null) {
                siteId = new Integer(params.siteId)
            }
        }

        return siteId
    }

    @Secured(['ROLE_USER'])
    @Transactional
    def getInstitutesForUser(){
        List instituteDetails
        String ipAddress
        if("yes".equals(params.app) && !"".equals(params.ipAddress)){
            ipAddress=params.ipAddress
        }else{
            ipAddress = utilService.getIPAddressOfClient(request)
        }
        instituteDetails = userManagementService.getInstitutesForUser(getSiteId(request),ipAddress)
        //print all the institute details
        instituteDetails.each{
            println it
        }
        if("yes".equals(params.app)){
            if(redisService.("userPendingTodoCount_"+springSecurityService.currentUser.username)==null) toDoService.pendingToDoCount()
            def json = [ 'institutes' : instituteDetails ? instituteDetails : "Nothing present",
                         "noOfPendingTodo":redisService.("userPendingTodoCount_"+springSecurityService.currentUser.username)]
            render json as JSON
        }else{
            return  instituteDetails;
        }
    }

    @Transactional
    def myLibrary() {
        Integer siteId = getSiteId(request)

        boolean wsSite=false, libWonder=false, arihant=false ,instituteLibrary=false,oswal=false,oswaal=false,showLibrary=false, showMyBooks=true
        if("Yes".equals(""+session["disableStore"])){
            if(springSecurityService.currentUser!=null) {
                if(session["hasBooks_"+springSecurityService.currentUser.username]==null){
                    BooksPermission booksPermission = BooksPermission.findByUsername(springSecurityService.currentUser.username)
                    if(booksPermission!=null) {
                        session["hasBooks_"+springSecurityService.currentUser.username] = "true"
                    }else{
                        session["hasBooks_"+springSecurityService.currentUser.username] = "false"
                    }
                }
                if("false".equals(""+session["hasBooks_"+springSecurityService.currentUser.username])) showMyBooks=false

            }
            else showMyBooks=false

        }
        if(siteId.intValue()==1) wsSite = true
        else if(siteId.intValue()==3) arihant=true
        else if(siteId.intValue()==25) libWonder=true
        else if(siteId.intValue()==22) oswal=true
        else if(siteId.intValue()==38) oswaal=true
        if(servletContext.getAttribute("googleLogEnabled")==null)
        {
            if("true".equals(dataProviderService.isGoogleAnayticsEnabled())) servletContext.setAttribute("googleLogEnabled","true")
            else servletContext.setAttribute("googleLogEnabled","false")
        }
        boolean hasDriveAccess = false
        boolean hasRaAccess = false
        String batchIdForStudio = null
        String batchIdForAira = null
        boolean instituteHasDriveAccess = false
        boolean showReferenceSection = false
        boolean enableTest = false
        def categories = null
        boolean isInstructor = false
        boolean enableQuestionPaper = false
        if(!"true".equals(session["NumberExceeded"])||((wsSite) && springSecurityService.currentUser!=null && userManagementService.isValidSession(springSecurityService.currentUser.username,session.getId()))) {
            List usersInstituteDtl=getInstitutesForUser()
            boolean isLibrarian = false
            if (session.getAttribute("userdetails") == null) {
                if(springSecurityService.currentUser!=null) {
                    session["userdetails"] = User.findByUsername(springSecurityService.currentUser.username)
                }

            }
            if(session["userdetails"]!=null) {
                User user = session["userdetails"]
                if (user.authorities.any {
                    it.authority == "ROLE_LIBRARY_ADMIN"||it.authority == "ROLE_INSTITUTE_MANAGER"
                }) {
                    isLibrarian = true
                }
                if(session.getAttribute("userdetails").authorities.any {
                    it.authority == "ROLE_INSTITUTE_MANAGER"
                }&&session["instituteManagerInstituteId"]==null) {
                    def instituteManagerInstituteId = userManagementService.getInstituteForInstituteManager(springSecurityService.currentUser.username, session["siteId"])
                    if(instituteManagerInstituteId!=-1) session["instituteManagerInstituteId"] = ""+instituteManagerInstituteId else session["instituteManagerInstituteId"] = null
                }
            }

            if(springSecurityService.currentUser!=null) {
                //check if the user is an instructor
                for (int i = 0; i < usersInstituteDtl.size(); i++) {
                    if ("true".equals("" + usersInstituteDtl[i].isInstructor)) {
                        isInstructor = true
                        break
                    }
                }
                for (int i = 0; i < usersInstituteDtl.size(); i++) {
                    if("true".equals(usersInstituteDtl[i].driveForInstructor)) {
                        instituteHasDriveAccess = true
                        if(isLibrarian) hasDriveAccess = true
                    }
                    if (("true".equals(usersInstituteDtl[i].driveForInstructor)&&"true".equals(""+usersInstituteDtl[i].isInstructor)) || "true".equals(usersInstituteDtl[i].driveForStudent)) {
                        hasDriveAccess = true
                        batchIdForStudio = ""+usersInstituteDtl[i].batchId
                        break
                    }
                }
                // do the same check for RA access
                for (int i = 0; i < usersInstituteDtl.size(); i++) {
                    if("true".equals(usersInstituteDtl[i].raForInstructor)) {
                        if(isLibrarian) hasRaAccess = true
                    }
                    if (("true".equals(usersInstituteDtl[i].raForInstructor)&&"true".equals(""+usersInstituteDtl[i].isInstructor)) || "true".equals(usersInstituteDtl[i].raForStudent)) {
                        hasRaAccess = true
                        batchIdForAira = ""+usersInstituteDtl[i].batchId
                        break
                    }
                }

                //do the same for reference section
                for (int i = 0; i < usersInstituteDtl.size(); i++) {
                    if(usersInstituteDtl[i].showReferenceSection!=null&&!"".equals(usersInstituteDtl[i].showReferenceSection)) {
                        categories = LibraryCategory.findAllByReferenceSectionId(new Integer(usersInstituteDtl[i].showReferenceSection))
                        showReferenceSection = true
                        break
                    }

                }

//do the same for test
                for (int i = 0; i < usersInstituteDtl.size(); i++) {
                    if ("true".equals(usersInstituteDtl[i].enableTest)) {
                        enableTest = true
                        session["enableTest"] = "true"
                        break
                    }
                }
                //do the same for question paper
                for (int i = 0; i < usersInstituteDtl.size(); i++) {
                    if ("true".equals(usersInstituteDtl[i].enableQuestionPaper)) {
                        enableQuestionPaper = true
                        session["enableQuestionPaper"] = "true"
                        break
                    }
                }
            }

            if (session.getAttribute("publisherLogoId")== null && libWonder&&usersInstituteDtl.size()>0) {
                for(int i=0;i<usersInstituteDtl.size();i++){
                    if(usersInstituteDtl[i].publisherId!=null){
                        session["publisherLogoId"]=""+usersInstituteDtl[i].publisherId;
                        break
                    }
                }
            }
            def showMyshelf=false,nameGreeting=false,showAccessCode=false
            if(libWonder || arihant ||oswal || oswaal||("true".equals(""+session["commonWhiteLabel"])&&!"Yes".equals(""+session["disableScratchCode"]))) {
                if(springSecurityService.currentUser!=null) {
                    nameGreeting = true
                    showAccessCode = true
                }
            }


            if(libWonder){
                showLibrary= utilService.hasLibraryAccess(request,25)
            }

            showMyshelf = true



            // active categories
            if (session["activeCategories_1"] == null) {
                wsshopService.activeCategories(new Integer(1))
                session["activeCategories_1"] = redisService.("activeCategories_1")
            }
            def siteMst = dataProviderService.getSiteMst(new Long(session['siteId']!=null?""+session['siteId']:"1"))
            if(siteMst.instituteLibrary=='true')instituteLibrary=true
            if(springSecurityService.currentUser!=null) {
                if (redisService.("usersCartBooksDetails_" + springSecurityService.currentUser.username) == null) {
                    dataProviderService.usersCartBooksDetailsByUsername(springSecurityService.currentUser.username, session["siteId"])
                }
                session["userCartCount"]=Integer.parseInt(redisService.("usersCartBooksDetailsCount_"+springSecurityService.currentUser.username))
                User user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
                if(user.chatTokensBalance!=null) {
                    session['chatTokensBalance'] = user.chatTokensBalance
                }

            }
            if(usersInstituteDtl!=null&&usersInstituteDtl.size()>0&&params.instituteId==null) params.put("instituteId",""+usersInstituteDtl[0].id)

            [siteMst: siteMst,institutes:usersInstituteDtl?usersInstituteDtl:null, showMyShelf:showMyshelf,nameGreeting:nameGreeting, commonTemplate:"true",showAccessCode:showAccessCode,
             instituteLibrary:instituteLibrary,showLibrary:showLibrary,isLibrarian:isLibrarian,showLibrary:utilService.hasLibraryAccess(request,siteId),librarySearch:'true',
             hasDriveAccess:hasDriveAccess,batchIdForStudio:batchIdForStudio,hasRaAccess:hasRaAccess,batchIdForAira:batchIdForAira,
             instituteHasDriveAccess:instituteHasDriveAccess,showReferenceSection:showReferenceSection,categories:categories,showMyBooks:showMyBooks,
             isInstructor:isInstructor,enableTest:enableTest,enableQuestionPaper:enableQuestionPaper]

        }
        else{
            Cookie cookie = new Cookie("SimulError", "Fail")
            cookie.path = "/"
            WebUtils.retrieveGrailsWebRequest().getCurrentResponse().addCookie(cookie)
            redirect(uri: "/logoff")
        }
    }

    @Transactional
    def getInstituteLastReadBooks(){
        def json
        if (springSecurityService.currentUser != null) {
            String defaultBatchId = wsLibraryService.getDefaultBatchId(params.batchId)
            if(springSecurityService.currentUser!=null) {
                if (redisService.("lastReadBooksIns_" + defaultBatchId + "_" + springSecurityService.currentUser.username) == null || redisService.("lastReadBooksIns_" + defaultBatchId + "_" + springSecurityService.currentUser.username) == "null") {
                    dataProviderService.getLastReadBooksForInstitute(springSecurityService.currentUser.username, defaultBatchId)
                }
            }
            json = ['lastReadBooksForInstitute':springSecurityService.currentUser!=null?redisService.("lastReadBooksIns_"+defaultBatchId+"_"+springSecurityService.currentUser.username):null]
        } else {
            json = ['lastReadBooksForInstitute': null]
        }
        render json as JSON
    }

    @Transactional
    def updateUserLibraryValidity(){
        BatchUserDtl batchUserDtl = BatchUserDtl.findByUsernameAndBatchId(params.username,new Integer(params.batchId))
        if(batchUserDtl!=null){
            Date validityDate = null
            DateFormat df = new SimpleDateFormat("dd-MM-yyyy");
            if (params.validityDate != null && params.validityDate != "") {
                validityDate = df.parse(params.validityDate)
                batchUserDtl.earlierDate = batchUserDtl.validityDate
                batchUserDtl.modifiedUsername = springSecurityService.currentUser.username
                batchUserDtl.validityDate = validityDate
                batchUserDtl.save(failOnError: true, flush: true)
            }
        }
    }

    //method to send email if the validity of the user is three days away. The data will be available from BatchUserDtl table
    @Transactional
    def sendValidityEmail(){
        //find all users where validityDate is not null. Can we modify the find method itself to get the users whose validityDate is three days away?
        //modify the find method itself to get the users whose validityDate is three days away

        def currentDate = new Date()
        def threeDaysFromNow = currentDate + 3
        def batchUserDtls = BatchUserDtl.findAllByValidityDateBetween(currentDate, threeDaysFromNow)
        def totalEmails = batchUserDtls.size()
        String fromEmail
        String validatyDate
        batchUserDtls.each{
            //send email
            //get the date in dd/mm/yyyy format
            validatyDate = (new SimpleDateFormat("dd-MM-yyyy")).format(it.validityDate)
            User user = User.findByUsername(it.username)
            SiteMst siteMst = dataProviderService.getSiteMst(user.siteId)
            if(siteMst.fromEmail!=null&&!"".equals(siteMst.fromEmail))
                fromEmail = siteMst.fromEmail
            else
                fromEmail = "Wonderslate <<EMAIL>>"
            if(user!=null&&user.email!=null) {
                String email = user.email
                String subjectText = "Validity of your library is about to expire"
                String body = "Your library validity is about to expire on "+validatyDate+" . Please renew your library validity."
                if("<EMAIL>"!=email) {
                    mailService.sendMail {
                        async true
                        to email
                        from fromEmail
                        subject subjectText
                        text body
                    }
                }
            }

        }
        render totalEmails

    }

    @Transactional @Secured(['ROLE_USER'])
    def getWonderslatePublishers(){
        if(redisService.("wonderslatePublishers")==null) {
            if (redisService.("siteIdList_1") == null) {
                dataProviderService.getSiteIdList(new Integer(1))
            }
            String siteIdList = redisService.("siteIdList_1")
            String sql = "select p.name" +
                    " from books_mst bk, books_tag_dtl btd, publishers p " +
                    " where bk.id=btd.book_id and bk.site_id in(" + siteIdList + ") and bk.status in ('free','published') " +
                    " and p.id=bk.publisher_id and p.id=bk.publisher_id"+
                    " group by name order by name "
            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
            def sql1 = new SafeSql(dataSource)
            def results = sql1.rows(sql)
            Gson gson = new Gson();
            String element = gson.toJson(results,new TypeToken<List>() {}.getType())
            redisService.("wonderslatePublishers") = element
        }
        def json = ["wonderslatePublishers":redisService.("wonderslatePublishers")]
        render json as JSON
    }


}
