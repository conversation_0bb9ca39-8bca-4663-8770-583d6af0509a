package wonderslate329

class UrlMappings {

    static mappings = {
        "/$controller/$action?/$id?(.$format)?"{
            constraints {
            }
        }
        "/"{
            controller = "books"
            action = "index"
        }
        "/index?" (controller: "books", action: "index")
        "/books/index?" (controller: "books", action: "index")
        "/books/store?" (controller: "books", action: "ebooks")
        "/sage?" (controller: "sage", action: "index")
        "/book/$bookId?" (controller: "sage", action: "index")
        "/store?" (controller: "books", action: "ebooks")
        "/evidya?" (controller: "evidya", action: "index")
        "/etexts?" (controller: "etexts", action: "index")
        "/ebouquet?" (controller: "ebouquet", action: "index")
        "/libwonder?" (controller: "libwonder", action: "index")
         "/funlearn/topicsMap?" (controller: "funlearn", action: "topicsMap")
        "/api/topicsMap?" (controller: "funlearn", action: "topicsMap")
        "/funlearn/chapterDetails?" (controller: "funlearn", action: "chapterDetails")
        "/api/chapterDetails?" (controller: "funlearn", action: "chapterDetails")
        "/funlearn/quizQuestionAnswers?" (controller: "funlearn", action: "quizQuestionAnswers")
        "/api/quizQuestionAnswers?" (controller: "funlearn", action: "quizQuestionAnswers")
        "/funlearn/quizQuestions?" (controller: "funlearn", action: "quizQuestions")
        "/api/quizQuestions?" (controller: "funlearn", action: "quizQuestions")
        "/funlearn/quizAnswers?" (controller: "funlearn", action: "quizAnswers")
        "/api/quizAnswers?" (controller: "funlearn", action: "quizAnswers")
        "/funlearn/download?" (controller: "funlearn", action: "download")
        "/api/download?" (controller: "funlearn", action: "download")
        "/funlearn/showQuizImage?" (controller: "funlearn", action: "showQuizImage")
        "/api/showQuizImage?" (controller: "funlearn", action: "showQuizImage")
        "/wonderpublish/getBooksListForUser?" (controller: "wonderpublish", action: "getBooksListForUser")
        "/api/getBooksListForUser?" (controller: "wonderpublish", action: "getBooksListForUser")
        "/wonderpublish/getChaptersList?" (controller: "wonderpublish", action: "getChaptersList")
        "/api/getChaptersList?" (controller: "wonderpublish", action: "getChaptersList")
        "/testgenerator/getUserBooks?" (controller: "wonderpublish", action: "getUserBooksForTestGenerator")
        "/api/getUserBooks?" (controller: "wonderpublish", action: "getUserBooksForTestGenerator")
        "/testgenerator/getTestChapters?" (controller: "testgenerator", action: "getTestChapters")
        "/api/getTestChapters?" (controller: "testgenerator", action: "getTestChapters")
        "/testgenerator/getTestQuizTypes?" (controller: "testgenerator", action: "getTestQuizTypes")
        "/api/getTestQuizTypes?" (controller: "testgenerator", action: "getTestQuizTypes")
        "/testgenerator/getTestDifficultyLevels?" (controller: "testgenerator", action: "getTestDifficultyLevels")
        "/api/getTestDifficultyLevels?" (controller: "testgenerator", action: "getTestDifficultyLevels")
        "/testgenerator/getTestNumberOfQuestions?" (controller: "testgenerator", action: "getTestNumberOfQuestions")
        "/api/getTestNumberOfQuestions?" (controller: "testgenerator", action: "getTestNumberOfQuestions")
        "/wonderpublish/updateReviewRating?" (controller: "wonderpublish", action: "updateReviewRating")
        "/api/updateReviewRating?" (controller: "wonderpublish", action: "updateReviewRating")
        "/creation/editUserProfile?" (controller: "creation", action: "editUserProfile")
        "/api/editUserProfile?" (controller: "creation", action: "editUserProfile")
        "/funlearn/updateWithQuizAnswers?" (controller: "funlearn", action: "updateWithQuizAnswers")
        "/api/updateWithQuizAnswers?" (controller: "funlearn", action: "updateWithQuizAnswers")
        "/funlearn/downloadUpdatedFile?" (controller: "funlearn", action: "downloadUpdatedFile")
        "/api/downloadUpdatedFile?" (controller: "funlearn", action: "downloadUpdatedFile")
        "/funlearn/getHtmls?" (controller: "funlearn", action: "getHtmls")
        "/api/getHtmls?" (controller: "funlearn", action: "getHtmls")
        "500"(controller: "wonderpublish", action: "errorPage")
        "404"(controller: "wonderpublish", action: "errorPage")
        "/wonderpublish/getChapterHtml/$bookId/$chapterId/$resId/$filename?" (controller: "wonderpublish", action: "getChapterHtml")
        "/analytics/getQuizScoreAnalytics?" (controller: "analytics", action: "getQuizScoreAnalytics")
        "/api/getQuizScoreAnalytics?" (controller: "analytics", action: "getQuizScoreAnalytics")
        "/api/appPurchase?" (controller: "wonderpublish", action: "purchase")
        "/wonderpublish/bookPurchased?" (controller: "wonderpublish", action: "bookPurchased")
        "/api/bookPurchased?" (controller: "wonderpublish", action: "bookPurchased")
        "/wonderpublish/bookCdValidate?" (controller: "wsshop", action: "bookCdValidate")
        "/api/bookCdValidate?" (controller: "wsshop", action: "bookCdValidate")
		"/smartebook/$bookId/$chapterId?/$id?/$description?" (controller: "wonderpublish", action: "book")
        "/smartebook/privacypolicy" (controller: "funlearn", action: "privacy")
        "/smartebook/termsandconditions" (controller: "funlearn", action: "termsandconditions")
        "/robots.txt" (view: "/robots")
        "/smartebooks/$level?/$syllabus?/$grade?/$subject?" (controller: "books", action: "ebooks")
        "/ebooks/$level?/$syllabus?/$grade?/$subject?" (controller: "books", action: "ebooks")
        "/publisher/$urlname" (controller: "books", action: "ebooks")
        "/api/getBooksReviewRatings?" (controller: "wonderpublish", action: "getBooksReviewRatings")
        "/wonderpublish/getBooksReviewRatings?" (controller: "wonderpublish", action: "getBooksReviewRatings")
        "/api/updateUserPassword?" (controller: "creation", action: "updateUserPassword")
        "/creation/updateUserPassword?" (controller: "creation", action: "updateUserPassword")
        "/api/updateUserProfile?" (controller: "creation", action: "updateUserProfile")
        "/creation/updateUserProfile?" (controller: "creation", action: "updateUserProfile")
        "/api/uploadProfileImage?" (controller: "creation", action: "uploadProfileImage")
        "/creation/uploadProfileImage?" (controller: "creation", action: "uploadProfileImage")
        "/api/userDetails?" (controller: "creation", action: "userDetails")
        "/creation/userDetails?" (controller: "creation", action: "userDetails")
        "/library?" (controller: "wonderpublish", action: "mybooks")
        "/register?" (controller: "funlearn", action: "signUp")
        "/publishing-desk?" (controller: "wonderpublish", action: "pubDesk")
        "/publishing-sales?" (controller: "reports", action: "pubSales")
        "/test-generator?" (controller: "wonderpublish", action: "testgenerator")
        "/library/$bookname?/$chaptername?" (controller: "wonderpublish", action:"book")
        "/$syllabusgrade/book/$bookname?" (controller: "wonderpublish", action:"bookdtl")
        "/$syllabusgrade/$bookname/book-details" (controller: "wonderpublish", action:"bookdtl")
        "/$syllabusgrade/$bookname/book" (controller: "wonderpublish", action:"book")
        "/$bookname/ebook-details" (controller: "resources", action:"eBookDtl")
        "/$bookname/ebook" (controller: "resources", action:"ebook")
        "/book-create/$bookname?" (controller: "wonderpublish", action:"bookCreate")
        "/book-create-new/$bookname?" (controller: "wonderpublish", action:"bookCreateNew")
        "/api/addFreeBookToLibrary?" (controller: "wonderpublish", action: "addFreeBookToLibrary")
        "/wonderpublish/addFreeBookToLibrary?" (controller: "wonderpublish", action: "addFreeBookToLibrary")
        "/api/getUserOrdersList?" (controller: "creation", action: "getUserOrdersList")
        "/creation/getUserOrdersList?" (controller: "creation", action: "getUserOrdersList")
        "/api/annotateSearch?" (controller: "wonderpublish", action: "annotateSearch")
        "/wonderpublish/annotateSearch?" (controller: "wonderpublish", action: "annotateSearch")
        "/api/annotateSave?" (controller: "wonderpublish", action: "annotateSave")
        "/wonderpublish/annotateSave?" (controller: "wonderpublish", action: "annotateSave")
        "/api/annotateUpdate?" (controller: "wonderpublish", action: "annotateUpdate")
        "/wonderpublish/annotateUpdate?" (controller: "wonderpublish", action: "annotateUpdate")
        "/api/annotateDestroy?" (controller: "wonderpublish", action: "annotateDestroy")
        "/wonderpublish/annotateDestroy?" (controller: "wonderpublish", action: "annotateDestroy")
        "/api/addlink?" (controller: "resourceCreator", action: "addlink")
        "/resourceCreator/addlink?" (controller: "resourceCreator", action: "addlink")
        "/api/searchList?" (controller: "discover", action: "searchList")
        "/discover/searchList?" (controller: "discover", action: "searchList")
        "/api/search?" (controller: "discover", action: "search")
        "/discover/search?" (controller: "discover", action: "search")
       "/edugorilla?" (controller: "edugorilla", action: "store")
        "/arivupro?" (controller: "arivupro", action: "store")
        "/api/getFlashCards?" (controller: "funlearn", action: "getFlashCards")
        "/funlearn/getFlashCards?" (controller: "funlearn", action: "getFlashCards")
        "/api/addKeyValues?" (controller: "wonderpublish", action: "addKeyValues")
        "/course/addKeyValues?" (controller: "wonderpublish", action: "addKeyValues")
        "/api/getFlashCardAsMCQQuestions?" (controller: "funlearn", action: "getFlashCardAsMCQQuestions")
        "/funlearn/getFlashCardAsMCQQuestions?" (controller: "funlearn", action: "getFlashCardAsMCQQuestions")
        "/api/getFlashCardAsTFQuestions?" (controller: "funlearn", action: "getFlashCardAsTFQuestions")
        "/funlearn/getFlashCardAsTFQuestions?" (controller: "funlearn", action: "getFlashCardAsTFQuestions")
        "/api/keyValueRecorder?" (controller: "funlearn", action: "keyValueRecorder")
        "/funlearn/keyValueRecorder?" (controller: "funlearn", action: "keyValueRecorder")
        "/api/getUserAnalyticsForStudySets?" (controller: "funlearn", action: "getUserAnalyticsForStudySets")
        "/funlearn/getUserAnalyticsForStudySets?" (controller: "funlearn", action: "getUserAnalyticsForStudySets")
        "/api/getAssignmentsForStudents?" (controller: "institute", action: "getAssignmentsForStudents")
        "/institute/getAssignmentsForStudents?" (controller: "institute", action: "getAssignmentsForStudents")
        "/api/getSharedQuizQuestionAnswers?" (controller: "funlearn", action: "getSharedQuizQuestionAnswers")
        "/funlearn/getSharedQuizQuestionAnswers?" (controller: "funlearn", action: "getSharedQuizQuestionAnswers")
        "/api/updateChapterAccess?" (controller: "funlearn", action: "updateChapterAccess")
        "/funlearn/updateChapterAccess?" (controller: "funlearn", action: "updateChapterAccess")
        "/api/getLastReadBooks?" (controller: "funlearn", action: "getLastReadBooks")
        "/funlearn/getLastReadBooks?" (controller: "funlearn", action: "getLastReadBooks")
        "/api/updateUserView?" (controller: "log", action: "updateUserView")
        "/log/updateUserView?" (controller: "log", action: "updateUserView")
        "/log/updateView?" (controller: "log", action: "updateView")
        "/api/generateOTP?" (controller: "creation", action: "generateOTP")
        "/wonderpublish/generateOTP?" (controller: "creation", action: "generateOTP")
        "/api/checkOTP?" (controller: "creation", action: "checkOTP")
        "/wonderpublish/checkOTP?" (controller: "creation", action: "checkOTP")
        "/api/getRankDetails?" (controller: "wonderpublish", action: "getRankDetails")
        "/wonderpublish/getRankDetails?" (controller: "wonderpublish", action: "getRankDetails")
        "/api/addQuizIssue?" (controller: "log", action: "addQuizIssue")
        "/log/addQuizIssue?" (controller: "log", action: "addQuizIssue")
        "/api/getTestSeriesDtl?" (controller: "wonderpublish", action: "getTestSeriesDtl")
        "/wonderpublish/getTestSeriesDtl?" (controller: "wonderpublish", action: "getTestSeriesDtl")
        "/api/removeBookFromLibrary?" (controller: "log", action: "removeBookFromLibrary")
        "/log/removeBookFromLibrary?" (controller: "log", action: "removeBookFromLibrary")
        "/api/removeResource?" (controller: "log", action: "removeResource")
        "/log/removeResource?" (controller: "log", action: "removeResource")
        "/funlearn/getHtmlsFile?" (controller: "funlearn", action: "getHtmlsFile")
        "/api/getHtmlsFile?" (controller: "funlearn", action: "getHtmlsFile")
        "/funlearn/downloadReadZippedFile?" (controller: "funlearn", action: "downloadReadZippedFile")
        "/api/downloadReadZippedFile?" (controller: "funlearn", action: "downloadReadZippedFile")
        "/api/insertDeviceId?" (controller: "log", action: "insertDeviceId")
        "/log/insertDeviceId?" (controller: "log", action: "insertDeviceId")
        "/api/loggedOutFromDevice?" (controller: "log", action: "loggedOutFromDevice")
        "/log/loggedOutFromDevice?" (controller: "log", action: "loggedOutFromDevice")
        "/api/collectChapterDetails?" (controller: "funlearn", action: "collectChapterDetails")
        "/funlearn/collectChapterDetails?" (controller: "funlearn", action: "collectChapterDetails")
        "/api/getPaidVideoURL?" (controller: "wonderpublish", action: "getPaidVideoURL")
        "/wonderpublish/getPaidVideoURL?" (controller: "wonderpublish", action: "getPaidVideoURL")
        "/api/getRelatedBooks?" (controller: "funlearn", action: "getRelatedBooks")
        "/funlearn/getRelatedBooks?" (controller: "funlearn", action: "getRelatedBooks")
        "/api/generateUmOTP?" (controller: "creation", action: "generateUmOTP")
        "/creation/generateUmOTP?" (controller: "creation", action: "generateUmOTP")
        "/api/checkUmOTP?" (controller: "creation", action: "checkUmOTP")
        "/creation/checkUmOTP?" (controller: "creation", action: "checkUmOTP")
        "/api/updateMigrateUserPassword?" (controller: "creation", action: "updateMigrateUserPassword")
        "/creation/updateMigrateUserPassword?" (controller: "creation", action: "updateMigrateUserPassword")
        "/api/switchUser?" (controller: "creation", action: "switchUser")
        "/creation/switchUser?" (controller: "creation", action: "switchUser")
        "/api/getLatestNotifications?" (controller: "log", action: "getLatestNotifications")
        "/log/getLatestNotifications?" (controller: "log", action: "getLatestNotifications")
        "/api/updateUserPreference?" (controller: "usermanagement", action: "updateUserPreference")
        "/usermanagement/updateUserPreference?" (controller: "usermanagement", action: "updateUserPreference")
        "/api/getUserPreferences?" (controller: "usermanagement", action: "getUserPreferences")
        "/usermanagement/getUserPreferences?" (controller: "usermanagement", action: "getUserPreferences")
        "/api/getBannerdetails?" (controller: "wonderpublish", action: "getBannerdetails")
        "/wonderpublish/getBannerdetails?" (controller: "wonderpublish", action: "getBannerdetails")
        "/api/getUserAndBooksDetails?" (controller: "wonderpublish", action: "getUserAndBooksDetails")
        "/api/getUserPaidBooksDetails?" (controller: "wonderpublish", action: "getUserPaidBooksDetails")
        "/wonderpublish/getUserPaidBooksDetails?" (controller: "wonderpublish", action: "getUserPaidBooksDetails")
        "/wonderpublish/getUserAndBooksDetails?" (controller: "wonderpublish", action: "getUserAndBooksDetails")
        "/payment/$razorPayId?" (controller: "wonderpublish", action: "selfService")
        "/api/getUserResources?" (controller: "usermanagement", action: "getUserResources")
        "/usermanagement/getUserResources?" (controller: "usermanagement", action: "getUserResources")
        "/api/getUserDetails?" (controller: "support", action: "getUserDetails")
       "/api/editProfile" (controller: "usermanagement", action: "updateUserProfile")
        "/usermanagement/updateUserProfile" (controller: "usermanagement", action: "updateUserProfile")

        "/api/getUserProfileDetails" (controller: "usermanagement", action: "index")
        "/usermanagement/index" (controller: "usermanagement", action: "index")

        "/api/getTopRankers" (controller: "usermanagement", action: "getTopRankers")
        "/usermanagement/getTopRankers" (controller: "usermanagement", action: "getTopRankers")
       "/api/getAnnotationsForFullBook?" (controller: "wonderpublish", action: "getAnnotationsForFullBook")
        "/wonderpublish/getAnnotationsForFullBook?" (controller: "wonderpublish", action: "getAnnotationsForFullBook")
       "/api/updateUserView" (controller: "log", action:"updateUserView")
        "/log/updateUserView" (controller: "log", action:"updateUserView")
        "/api/newQuizQA" (controller: "funlearn", action:"newQuizQA")
        "/funlearn/newQuizQA" (controller: "funlearn", action:"newQuizQA")
        "/api/isUserBlocked" (controller: "comments", action:"isUserBlocked")
        "/comments/isUserBlocked" (controller: "comments", action:"isUserBlocked")
        "/institution/$urlname" (controller: "institute", action: "index")
        "/registration" (controller: "log", action:"enquiryForm")
        "/api/getInstitutesForUser" (controller: "books", action:"getInstitutesForUser")
        "/books/getInstitutesForUser" (controller: "books", action:"getInstitutesForUser")
        "/api/eBookDtlForApp" (controller: "resources", action:"eBookDtlForApp")
        "/resources/eBookDtlForApp" (controller: "resources", action:"eBookDtlForApp")
        "/api/addFeedback" (controller: "log", action:"addFeedback")
        "/log/addFeedback" (controller: "log", action:"addFeedback")
        "/instituteHome" (controller: "institute", action: "home")
         "/api/quizSubmit" (controller: "prepjoy", action:"quizSubmit")
        "/prepjoy/quizSubmit" (controller: "prepjoy", action:"quizSubmit")
        "/api/getUserPrepJoyDetails" (controller: "prepjoy", action:"getUserPrepJoyDetails")
        "/prepjoy/getUserPrepJoyDetails" (controller: "prepjoy", action:"getUserPrepJoyDetails")
        "/api/addUserNewsLangPreference" (controller: "news", action:"addUserNewsLangPreference")
        "/news/addUserNewsLangPreference" (controller: "news", action:"addUserNewsLangPreference")
        "/api/addUserNewsSourcePreference" (controller: "news", action:"addUserNewsSourcePreference")
        "/news/addUserNewsSourcePreference" (controller: "news", action:"addUserNewsSourcePreference")
        "/api/getUserTests" (controller: "funlearn", action:"getUserTests")
        "/funlearn/getUserTests" (controller: "funlearn", action:"getUserTests")
        "/api/getUserAnswerDetails" (controller: "funlearn", action:"getUserAnswerDetails")
        "/funlearn/getUserAnswerDetails" (controller: "funlearn", action:"getUserAnswerDetails")
        "/current-affairs" (controller: "prepjoy", action: "dailyCurrentAffairs")
        "/resources/ebook" (controller: "resources", action: "ebook")
        "/currentaffairs" (controller: "prepjoy", action: "currentaffairs")
                {
                    urlName="currentaffairs"
                }
        "/publishTests" (controller: "resources", action: "flashCardHome")
                {
                    resType="Multiple Choice Questions"
                }

        "/classroom/$instituteId/$groupId?" (controller: "groups", action: "groupDtl")
        "/api/getResViewByUser" (controller: "log", action:"getResViewByUser")
        "/log/getResViewByUser" (controller: "log", action:"getResViewByUser")
        "/api/getRevisionListByUser" (controller: "log", action:"getRevisionListByUser")
        "/log/getRevisionListByUser" (controller: "log", action:"getRevisionListByUser")
        "/api/addUserLoginLog" (controller: "log", action:"addUserLoginLog")
        "/log/addUserLoginLog" (controller: "log", action:"addUserLoginLog")
        "/apps" (controller: "books", action:"appLink")
        "/products" (controller: "books", action: "publishersProduct")
        "/inst/$redirectionTarget?" (controller: "whitelabel", action:"redirector")
         {
             redirectionType="inst"
        }

        "/trans/$transId?" (controller: "books", action:"userOrderProcess")
        "/favouriteteachers/$nomineeId?" (controller: "usermanagement", action: "nomineeDetails")
        "/usermanagement/teachersNomineeForm/" (controller: "usermanagement", action: "teachersPollResult")
        "/exOrder/$transId?" (controller: "intelligence", action:"outsidePurchaseRedirector")
        "/wsLibrary/myLibrary?" (controller: "libraryBooks", action:"myLibrary")
        "/$testName/online-test" (controller: "mocktests", action: "index")
        "/$testName/online-exam" (controller: "mocktests", action: "index")
        "/online-tests" (controller: "mocktests", action: "index")
        "/prepjoy/dailyTest" (controller: "mocktests", action: "index")
        "/prepjoy/leaderBoard/" (controller: "books",action: "leaderboard")
        "/api/updateUserBookDurationLog" (controller: "usermanagement", action:"updateUserBookDurationLog")
        "/usermanagement/addUserLoginLog" (controller: "usermanagement", action:"updateUserBookDurationLog")
        "/api/getHomeInfo" (controller: "progress", action: "getHomeInfo")
        "/progress/getHomeInfo" (controller: "progress", action: "getHomeInfo")
        "/sp/$siteName" (controller: "privatelabel", action: "index")
        "/sp/$siteName/index" (controller: "privatelabel", action: "index")
        "/sp/$siteName/store" (controller: "privatelabel", action: "store")
        "/sp/$siteName/aistore" (controller: "privatelabel", action: "aiStore")
        "/prepjoy/store" (controller: "prepjoy", action: "eBooks")
        "/isbn/$isbn?" (controller: "resources", action:"eBookDtl")
        "/$siteName/page/$link" (controller: "privatelabel", action:"customPage")
        "/api/deleteUserByUser?" (controller: "admin", action: "deleteUserByUser")
        "/admin/deleteUserByUser?" (controller: "admin", action: "deleteUserByUser")
        "/api/addBillShipAddressToHolder?" (controller: "wsshop", action: "addBillShipAddressToHolder")
        "/wsshop/addBillShipAddressToHolder?" (controller: "wsshop", action: "addBillShipAddressToHolder")
        "/prepjoy/store?" (controller: "prepjoy", action: "eBooks")
        "/$publisherName/publisher/$publisherId/$booksTagId?/$booksTagLevel?" (controller: "books", action: "ebooks"){
            linkSource="store"
        }
        "/$levelsName/books/$booksTagId/$booksTagLevel/$publisherId?" (controller: "books", action: "ebooks"){
            linkSource="store"
        }
        "/$levelsName/books/sp/$siteName/$booksTagId/$booksTagLevel/$publisherId?" (controller: "privatelabel", action: "store"){
            linkSource="store"
        }
        "/$levelsName/books/oswaal/$booksTagId/$booksTagLevel/$publisherId?" (controller: "oswaal", action: "store"){
            linkSource="store"
        }
        "/$levelsName/books/radianbooks/$booksTagId/$booksTagLevel/$publisherId?" (controller: "radianbooks", action: "store"){
            linkSource="store"
        }
        "/$levelsName/books/oswalpublisher/$booksTagId/$booksTagLevel/$publisherId?" (controller: "oswalpublisher", action: "store"){
            linkSource="store"
        }
        "/$levelsName/books/edugorilla/$booksTagId/$booksTagLevel/$publisherId?" (controller: "edugorilla", action: "store"){
            linkSource="store"
        }
        "/$levelsName/books/mtg/$booksTagId/$booksTagLevel/$publisherId?" (controller: "mtg", action: "store"){
            linkSource="store"
        }
        "/$levelsName/books/prepjoy/$booksTagId/$booksTagLevel/$publisherId?" (controller: "prepjoy", action: "eBooks"){
            linkSource="store"
        }
        "/pdf-to-epub" (controller: "books", action: "pdftoepub")
        "/app-for-institutions" (controller: "institute",action: "instituteProduct")
        "/$examGroup/mocktests" (controller: "mocktests", action: "listExams")
        "/$examName/mocktests/$dateRange/$dailyTestId" (controller: "mocktests", action: "examPage")

        "/$blogName/information/s/$syllabusId?" (controller: "wsshop", action: "blogEnglish"){
            dataSource="syllabus"
        }
        "/$blogName/information/g/$gradeId?" (controller: "wsshop", action: "blogEnglish"){
            dataSource="grade"
        }
        "/$blogName/information/su/$subjectId?" (controller: "wsshop", action: "blogEnglish"){
            dataSource="subject"
        }
        "/$blogName/hindi/s/$syllabusId?" (controller: "wsshop", action: "blogHindi"){
            dataSource="syllabus"
        }
        "/$blogName/hindi/g/$gradeId?" (controller: "wsshop", action: "blogHindi"){
            dataSource="grade"
        }
        "/$blogName/hindi/su/$subjectId?" (controller: "wsshop", action: "blogHindi"){
            dataSource="subject"
        }
        "/$blogName/books/g/$gradeId/$publisherId?" (controller: "articles", action: "getGradeBooks")
        "/$blogName/books/su/$subjectId/$publisherId?" (controller: "articles", action: "getGradeBooks")
        "/$bookTitle/free-pdf-download/$bookId" (controller: "resources", action: "previewChapter")
        "/$publisherName/seller/$pubId/$categoryId?" (controller: "printbooks", action: "index")
        "/books/contactus" (controller: "books", action: "ebooks")
        "/privatelabel/contactus" (controller: "privatelabel", action: "index")
        "/aistore" (controller: "privatelabel", action: "aiStore"){
            siteName="gptsir"
        }
    }
}
