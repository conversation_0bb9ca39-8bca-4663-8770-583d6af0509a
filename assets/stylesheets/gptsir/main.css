* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif !important;
  background: #f8f8f6;
  min-height: 100vh;
  color: #1e293b !important;
  line-height: 1.6 !important;
}
.container {
  max-width: 1200px !important;
  margin: 0 auto;
  padding: 0 20px !important;
}
main {
  padding-top: 60px;
  text-align: center;
}
.hero {
  padding: 100px 0 80px;
  background: #fff;
  min-height: 80vh;
  display: flex;
  align-items: center;
}
.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
  width: 100%;
}
.hero-image {
  display: flex;
  justify-content: center;
  align-items: center;
}
.hero-img {
  width: 100%;
  max-width: 500px;
  height: auto;
  border-radius: 20px;
}
.hero-text {
  text-align: left;
}
.hero-title {
  font-size: 54px;
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 16px;
  color: #1e293b;
}
.hero-title .highlight {
  color: #ff6b35;
}
.hero-subtitle-main {
  font-size: 36px;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 24px;
  color: #1e293b;
}
.hero-description {
  font-size: 18px;
  color: #64748b;
  line-height: 1.6 !important;
  margin-bottom: 32px;
  max-width: 500px;
}
.hero-cta {
  margin-top: 8px;
}
.hero-subtitle {
  font-size: 18px;
  color: #64748b;
  max-width: 600px;
  margin: 0 auto 40px;
  line-height: 1.6;
}
.cta-button {
  background-color: #ff6b35;
  color: white;
  padding: 14px 28px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600 !important;
  font-size: 16px;
  display: inline-block;
  transition: background-color 0.2s;
  margin-bottom: 8px;
  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.2);
}
.cta-button:hover {
  background-color: #e55a2b !important;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(255, 107, 53, 0.3);
}
.cta-note {
  font-size: 12px;
  color: #94a3b8;
  margin-top: 8px;
}
.section-bg-pattern-1 {
  background: #f8f8f6;
  position: relative;
}
.section-bg-pattern-1 > .container {
  position: relative;
  z-index: 1;
}
.section-bg-pattern-2 {
  background: #fff;
  position: relative;
}
.section-bg-pattern-2 > .container {
  position: relative;
  z-index: 1;
}
.mindmap-section {
  padding: 80px 0;
  position: relative;
  overflow: hidden;
  align-items: center;
  gap: 60px;
}
.mindmap-content {
  flex: 1;
}
.mindmap-content .section-title {
  text-align: center;
}
.section-title {
  font-size: 36px;
  font-weight: 700;
  text-align: left;
  margin-bottom: 16px;
  color: #1e293b;
  position: relative;
  z-index: 1;
}
.section-title .highlight {
  color: #ff6b35;
}
.section-subtitle {
  font-size: 18px;
  color: #64748b;
  text-align: left;
  margin-bottom: 0;
  position: relative;
  z-index: 1;
}
.mindmap-container {
  position: relative;
  height: 700px;
  flex-shrink: 0;
  z-index: 1;
}
.features-rotating-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 600px;
  height: 600px;
  transition: transform 1.5s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}
.central-node {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
  background: #fff;
  border-radius: 50%;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  animation: pulse 3s infinite;
}
.central-content {
  text-align: center;
  padding: 20px;
}
.central-icon {
  font-size: 40px;
  margin-bottom: 10px;
}
.central-node h3 {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}
.feature-node {
  position: absolute;
  width: 280px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
  padding: 20px;
  transition: all 1.5s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  transform: translateY(20px);
}
.feature-node.active {
  opacity: 1;
  transform: translateY(0);
}
.node-content {
  position: relative;
  z-index: 2;
}
.node-icon {
  font-size: 24px;
  margin-bottom: 12px;
}
.feature-node h4 {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 8px 0;
}
.feature-node p {
  font-size: 14px;
  color: #64748b;
  margin: 0;
  line-height: 1.5;
}
.node-connector {
  display: none;
  /* Hide connecting lines for clean design */
}
.feature-1 {
  position: absolute;
  top: -50px;
  left: 50%;
  transform: translateX(-50%) translateY(20px);
  opacity: 0;
  transition: opacity 0.8s cubic-bezier(0.4, 0, 0.2, 1), transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}
.feature-1.active {
  transform: translateX(-50%) translateY(0);
  opacity: 1;
}
.feature-2 {
  position: absolute;
  top: 50%;
  right: -150px;
  transform: translateY(-50%) translateX(20px);
  opacity: 0;
  transition: opacity 0.8s cubic-bezier(0.4, 0, 0.2, 1), transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}
.feature-2.active {
  transform: translateY(-50%) translateX(0);
  opacity: 1;
}
.feature-3 {
  position: absolute;
  bottom: -40px;
  left: 50%;
  transform: translateX(-50%) translateY(-20px);
  opacity: 0;
  transition: opacity 0.8s cubic-bezier(0.4, 0, 0.2, 1), transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}
.feature-3.active {
  transform: translateX(-50%) translateY(0);
  opacity: 1;
}
.feature-4 {
  position: absolute;
  top: 50%;
  left: -150px;
  transform: translateY(-50%) translateX(-20px);
  opacity: 0;
  transition: opacity 0.8s cubic-bezier(0.4, 0, 0.2, 1), transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}
.feature-4.active {
  transform: translateY(-50%) translateX(0);
  opacity: 1;
}
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 107, 53, 0.4);
  }
  70% {
    box-shadow: 0 0 0 15px rgba(255, 107, 53, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 107, 53, 0);
  }
}
.features-rotating-container.rotate-step-1 {
  transform: translate(-50%, -50%) rotate(90deg);
}
.features-rotating-container.rotate-step-1 .feature-4 {
  left: -90px;
}
.features-rotating-container.rotate-step-1 .feature-3 {
  bottom: -100px;
}
.features-rotating-container.rotate-step-1 .feature-1 {
  top: -110px;
}
.features-rotating-container.rotate-step-1 .feature-2 {
  right: -100px;
}
.features-rotating-container.rotate-step-1 .feature-1.active {
  transform: translateX(-50%) translateY(0) rotate(-90deg);
}
.features-rotating-container.rotate-step-1 .feature-2.active {
  transform: translateY(-50%) translateX(0) rotate(-90deg);
}
.features-rotating-container.rotate-step-1 .feature-3.active {
  transform: translateX(-50%) translateY(0) rotate(-90deg);
}
.features-rotating-container.rotate-step-1 .feature-4.active {
  transform: translateY(-50%) translateX(0) rotate(-90deg);
}
.features-rotating-container.rotate-step-2 {
  transform: translate(-50%, -50%) rotate(180deg);
}
.features-rotating-container.rotate-step-2 .feature-1.active {
  transform: translateX(-50%) translateY(0) rotate(-180deg);
}
.features-rotating-container.rotate-step-2 .feature-2.active {
  transform: translateY(-50%) translateX(0) rotate(-180deg);
}
.features-rotating-container.rotate-step-2 .feature-3.active {
  transform: translateX(-50%) translateY(0) rotate(-180deg);
}
.features-rotating-container.rotate-step-2 .feature-4.active {
  transform: translateY(-50%) translateX(0) rotate(-180deg);
}
.features-rotating-container.rotate-step-3 {
  transform: translate(-50%, -50%) rotate(270deg);
}
.features-rotating-container.rotate-step-3 .feature-4 {
  left: -100px;
}
.features-rotating-container.rotate-step-3 .feature-3 {
  bottom: -100px;
}
.features-rotating-container.rotate-step-3 .feature-1 {
  top: -100px;
}
.features-rotating-container.rotate-step-3 .feature-2 {
  right: -90px;
}
.features-rotating-container.rotate-step-3 .feature-1.active {
  transform: translateX(-50%) translateY(0) rotate(-270deg);
}
.features-rotating-container.rotate-step-3 .feature-2.active {
  transform: translateY(-50%) translateX(0) rotate(-270deg);
}
.features-rotating-container.rotate-step-3 .feature-3.active {
  transform: translateX(-50%) translateY(0) rotate(-270deg);
}
.features-rotating-container.rotate-step-3 .feature-4.active {
  transform: translateY(-50%) translateX(0) rotate(-270deg);
}
.features-rotating-container.rotate-step-4 {
  transform: translate(-50%, -50%) rotate(360deg);
}
.features-rotating-container.rotate-step-4 .feature-1.active {
  transform: translateX(-50%) translateY(0) rotate(0deg);
}
.features-rotating-container.rotate-step-4 .feature-2.active {
  transform: translateY(-50%) translateX(0) rotate(0deg);
}
.features-rotating-container.rotate-step-4 .feature-3.active {
  transform: translateX(-50%) translateY(0) rotate(0deg);
}
.features-rotating-container.rotate-step-4 .feature-4.active {
  transform: translateY(-50%) translateX(0) rotate(360deg);
}
.ai-features-section {
  padding: 80px 0;
}
.ai-features-section .section-title {
  text-align: center;
}
.ai-features-container {
  display: flex;
  align-items: flex-start;
  gap: 80px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}
.ai-features-content {
  flex: 1;
  max-width: 500px;
}
.ai-features-content .section-title {
  font-size: 36px;
  font-weight: 700;
  text-align: left;
  margin-bottom: 16px;
  color: #1e293b;
}
.ai-features-content .section-subtitle {
  font-size: 18px;
  color: #64748b;
  text-align: left;
}
.learning-modes-section .section-title {
  text-align: center;
}
.features-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}
.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  opacity: 0;
  transform: translateX(-30px);
  transition: all 0.6s ease;
}
.feature-item.active {
  opacity: 1;
  transform: translateX(0);
}
.feature-icon {
  font-size: 20px;
  margin-top: 2px;
  flex-shrink: 0;
}
.feature-text {
  text-align: justify;
}
.feature-text h4 {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 4px 0;
}
.feature-text p {
  font-size: 14px;
  color: #64748b;
  margin: 0;
  line-height: 1.5;
}
.comparison-table-container {
  flex: 1;
  max-width: 600px;
}
.comparison-table {
  width: 100%;
  border-collapse: collapse;
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
  margin-bottom: 24px;
}
.comparison-table th {
  background: #f8fafc;
  padding: 16px 20px;
  font-weight: 600;
  color: #1e293b;
  font-size: 14px;
  border-bottom: 1px solid #e2e8f0;
}
.comparison-table th.gptsir-column {
  background: #ffeee8;
  color: #ff6b35;
}
.comparison-table td {
  padding: 16px 20px;
  border-bottom: 1px solid #f1f5f9;
  font-size: 14px;
}
.table-row {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}
.table-row.active {
  opacity: 1;
  transform: translateY(0);
}
.table-row:hover {
  background-color: #f8fafc;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}
.feature-name {
  font-weight: 600;
  color: #1e293b;
}
.traditional-value {
  color: #64748b;
}
.gptsir-value {
  color: #1e293b;
  font-weight: 500;
  background: #fefefe;
}
.check {
  color: #10b981;
}
.cross {
  color: #ef4444;
}
.table-note {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  background: #fff7ed;
  padding: 20px;
  border-radius: 12px;
  border-left: 4px solid #ff6b35;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s ease;
}
.table-note.active {
  opacity: 1;
  transform: translateY(0);
}
.table-note p {
  margin: 0;
  font-size: 14px;
  color: #1e293b;
  line-height: 1.5;
}
.table-note p strong {
  color: #ff6b35;
}
.note-icon {
  font-size: 20px;
  margin-top: 2px;
  flex-shrink: 0;
}
.learning-modes-section {
  padding: 80px 0;
  position: relative;
  overflow: hidden;
}
.learning-modes-header {
  text-align: center;
  margin-bottom: 60px;
}
.learning-modes-header .section-title {
  font-size: 36px;
  font-weight: 700;
  color: #1e293b;
  line-height: 1.2;
  margin: 0;
}
.learning-modes-carousel {
  max-width: 1000px;
  margin: 0 auto;
  position: relative;
}
.carousel-container {
  position: relative;
  overflow: hidden;
  border-radius: 24px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  background: #fff;
}
.carousel-track {
  display: flex;
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  width: 300%;
  will-change: transform;
}
.mode-card {
  width: 33.333%;
  flex-shrink: 0;
  display: grid;
  grid-template-columns: 1fr 1fr;
  align-items: center;
  gap: 40px;
  padding: 60px;
  min-height: 300px;
  background: #fff;
  border: 2px solid #f1f5f9;
  position: relative;
  overflow: hidden;
}
.mode-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #ff6b35, #ff8c42);
  transform: scaleX(0);
  transition: transform 0.6s ease;
}
.mode-card.active::before {
  transform: scaleX(1);
}
.mode-card-content {
  position: relative;
  z-index: 2;
}
.mode-icon {
  font-size: 56px;
  margin-bottom: 20px;
  display: block;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}
.mode-title {
  font-size: 32px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 16px;
  line-height: 1.2;
}
.mode-description {
  font-size: 18px;
  color: #64748b;
  line-height: 1.6;
  margin: 0;
}
.mode-image-wrapper {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}
.mode-image {
  width: 100%;
  max-width: 350px;
  height: 350px;
  object-fit: contain;
  border-radius: 16px;
  transition: transform 0.3s ease;
}
.mode-card.active .mode-image {
  transform: scale(1.02);
}
.carousel-dots {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-top: 40px;
}
.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 1px solid #999;
  background: #cbd5e1;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}
.dot:hover {
  background: #94a3b8;
  transform: scale(1.1);
}
.dot.active {
  background: #ff6b35;
  transform: scale(1.2);
}
.dot.active::after {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border: 2px solid rgba(255, 107, 53, 0.3);
  border-radius: 50%;
  animation: pulse 2s infinite;
}
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}
.testimonials-section {
  padding: 80px 0;
  text-align: center;
}
.testimonials-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}
.testimonials-header {
  margin-bottom: 60px;
}
.testimonials-header .section-title {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 16px;
  color: #1e293b;
  text-align: center;
}
.testimonials-header .section-subtitle {
  font-size: 18px;
  color: #64748b;
  margin: 0;
  text-align: center;
}
.testimonials-scroll-container {
  overflow-x: auto;
  overflow-y: hidden;
  padding: 20px 0;
  margin: 0 -20px;
  scrollbar-width: none;
  -ms-overflow-style: none;
  position: relative;
  --scroll-indicator-opacity: 0.8;
}
.testimonials-scroll-container::-webkit-scrollbar {
  display: none;
}
@keyframes pulseScroll {
  0%,
  100% {
    opacity: 0.8;
    transform: translateY(-50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translateY(-50%) scale(1.1);
  }
}
.testimonials-grid {
  display: flex;
  gap: 24px;
  padding: 0 20px;
  min-width: max-content;
}
.testimonial-card {
  background: #fff;
  border-radius: 20px;
  padding: 32px 28px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid #f1f5f9;
  transition: all 0.4s ease;
  opacity: 0;
  transform: translateY(30px);
  min-width: 320px;
  max-width: 320px;
  position: relative;
  overflow: hidden;
}
.testimonial-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #ff6b35, #ff8c42);
  transform: scaleX(0);
  transition: transform 0.4s ease;
}
.testimonial-card::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 107, 53, 0.03) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.4s ease;
  pointer-events: none;
}
.testimonial-card.active {
  opacity: 1;
  transform: translateY(0);
  animation: slideInUp 0.6s ease forwards;
}
.testimonial-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.12);
}
.testimonial-card:hover::before {
  transform: scaleX(1);
}
.testimonial-card:hover::after {
  opacity: 1;
}
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}
.popular-plan {
  animation: float 6s ease-in-out infinite;
}
.testimonial-content {
  text-align: left;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.quote-icon {
  font-size: 48px;
  color: #ff6b35;
  font-family: Georgia, serif;
  line-height: 1;
  margin-bottom: 16px;
  opacity: 0.8;
}
.testimonial-text {
  font-size: 16px;
  line-height: 1.6;
  color: #1e293b;
  margin-bottom: 24px;
  flex: 1;
  font-weight: 500;
}
.testimonial-author {
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.author-name {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
}
.author-location {
  font-size: 13px;
  color: #64748b;
  font-weight: 400;
}
.pricing-section {
  padding: 100px 0;
  text-align: center;
  position: relative;
  display: none !important;
}
.pricing-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 80%, rgba(255, 107, 53, 0.03) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 107, 53, 0.03) 0%, transparent 50%), radial-gradient(circle at 40% 40%, rgba(139, 92, 246, 0.02) 0%, transparent 50%);
  pointer-events: none;
}
.pricing-section::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 25% 25%, #ff6b35 1px, transparent 1px), radial-gradient(circle at 75% 75%, #8b5cf6 1px, transparent 1px);
  background-size: 100px 100px, 150px 150px;
  opacity: 0.1;
  animation: float 20s ease-in-out infinite;
  pointer-events: none;
}
.pricing-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 1;
}
.pricing-header {
  margin-bottom: 80px;
}
.pricing-header .section-title {
  font-size: 42px;
  font-weight: 700;
  margin-bottom: 20px;
  color: #1e293b;
  text-align: center;
}
.pricing-header .section-subtitle {
  font-size: 20px;
  color: #64748b;
  margin: 0;
  text-align: center;
}
.pricing-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  max-width: 900px;
  margin: 0 auto;
}
.pricing-card {
  background: #fff;
  border-radius: 20px;
  padding: 28px 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 2px solid #f1f5f9;
  transition: all 0.4s ease;
  opacity: 0;
  transform: translateY(40px);
  position: relative;
  overflow: hidden;
  min-height: 480px;
  display: flex;
  flex-direction: column;
}
.pricing-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(90deg, #e2e8f0, #cbd5e1);
  transition: all 0.4s ease;
}
.pricing-card.active {
  opacity: 1;
  transform: translateY(0);
  animation: slideInUp 0.8s ease forwards;
}
.pricing-card:hover {
  transform: translateY(-12px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  border-color: #ff6b35;
}
.pricing-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, rgba(255, 107, 53, 0.05) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.4s ease;
  pointer-events: none;
  border-radius: 24px;
}
.pricing-card:hover::after {
  opacity: 1;
}
.starter-plan::before {
  background: linear-gradient(90deg, #64748b, #475569);
}
.starter-plan:hover::before {
  background: linear-gradient(90deg, #ff6b35, #ff8c42);
}
.popular-plan {
  border-color: #ff6b35;
  box-shadow: 0 12px 40px rgba(255, 107, 53, 0.15);
  transform: scale(1.02);
}
.popular-plan::before {
  background: linear-gradient(90deg, #ff6b35, #ff8c42);
}
.popular-plan:hover {
  transform: scale(1.02) translateY(-12px);
  box-shadow: 0 24px 64px rgba(255, 107, 53, 0.25);
}
.popular-plan .plan-price {
  background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
}
.popular-plan .amount {
  color: #ff6b35;
}
.popular-plan .custom-price .currency {
  color: #ff6b35;
}
.popular-plan .custom-price .amount {
  color: #ff6b35;
}
.popular-badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #ff6b35, #ff8c42);
  color: white;
  padding: 8px 24px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  box-shadow: 0 4px 16px rgba(255, 107, 53, 0.3);
  animation: pulse 2s ease-in-out infinite;
}
@keyframes pulse {
  0%,
  100% {
    box-shadow: 0 4px 16px rgba(255, 107, 53, 0.3);
  }
  50% {
    box-shadow: 0 4px 20px rgba(255, 107, 53, 0.5);
  }
}
.enterprise-plan::before {
  background: linear-gradient(90deg, #8b5cf6, #a855f7);
}
.enterprise-plan:hover::before {
  background: linear-gradient(90deg, #7c3aed, #8b5cf6);
}
.plan-header {
  text-align: center;
  margin-bottom: 24px;
}
.plan-icon {
  font-size: 36px;
  margin-bottom: 12px;
  display: block;
}
.plan-name {
  font-size: 20px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 8px;
}
.plan-description {
  font-size: 14px;
  color: #64748b;
  margin: 0;
  font-weight: 500;
}
.plan-price {
  text-align: center;
  margin-bottom: 24px;
  padding: 16px 0;
  border-radius: 12px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 80px;
}
.currency {
  font-size: 16px;
  font-weight: 600;
  color: #64748b;
  vertical-align: top;
}
.amount {
  font-size: 32px;
  font-weight: 800;
  color: #1e293b;
  margin: 0 2px;
  line-height: 1;
}
.period {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}
.custom-price {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}
.custom-price .currency {
  font-size: 18px;
  font-weight: 600;
  color: #64748b;
  margin: 0;
}
.custom-price .amount {
  font-size: 28px;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  line-height: 1;
}
.plan-features {
  margin-bottom: 24px;
  text-align: left;
  flex: 1;
}
.plan-features .feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  padding: 4px 0;
}
.feature-icon {
  font-size: 14px;
  color: #10b981;
  flex-shrink: 0;
  transition: transform 0.3s ease;
}
.pricing-card:hover .feature-icon {
  transform: scale(1.2);
}
.feature-text {
  font-size: 14px;
  color: #1e293b;
  font-weight: 500;
  line-height: 1.4;
}
.plan-button {
  width: 100%;
  padding: 12px 20px;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: auto;
}
.starter-button {
  background: linear-gradient(135deg, #64748b, #475569);
  color: white;
}
.starter-button:hover {
  background: linear-gradient(135deg, #475569, #334155);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(71, 85, 105, 0.3);
}
.popular-button {
  background: linear-gradient(135deg, #ff6b35, #ff8c42);
  color: white;
  box-shadow: 0 4px 16px rgba(255, 107, 53, 0.3);
}
.popular-button:hover {
  background: linear-gradient(135deg, #ff5722, #ff6b35);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(255, 107, 53, 0.4);
}
.enterprise-button {
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
  color: white;
}
.enterprise-button:hover {
  background: linear-gradient(135deg, #7c3aed, #8b5cf6);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(139, 92, 246, 0.3);
}
.categories-section {
  padding: 80px 0;
}
.categories-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}
.categories-header {
  text-align: center;
  margin-bottom: 60px;
}
.categories-header .section-title {
  font-size: 42px;
  font-weight: 700;
  margin-bottom: 16px;
  color: #1e293b;
  text-align: center;
}
.categories-header .section-subtitle {
  font-size: 18px;
  color: #64748b;
  margin: 0;
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}
.category-group {
  margin-bottom: 50px;
}
.category-group:last-child {
  margin-bottom: 0;
}
.category-title {
  font-size: 28px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 24px;
  text-align: left;
  position: relative;
  padding-bottom: 8px;
  display: flex;
  justify-content: flex-start;
  text-decoration: none;
}
.category-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #ff6b35, #ff8c42);
  border-radius: 2px;
}
.category-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-bottom: 20px;
  max-width: 900px;
}
.category-card {
  background: #fff;
  border-radius: 16px;
  padding: 24px 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  border: 2px solid #f1f5f9;
  transition: all 0.3s ease;
  cursor: pointer;
  text-decoration: none;
  color: inherit;
  display: block;
  position: relative;
  overflow: hidden;
}
.category-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #e2e8f0, #cbd5e1);
  transition: all 0.3s ease;
}
.category-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
  border-color: #ff6b35;
  text-decoration: none;
  color: inherit;
}
.category-card:hover::before {
  background: linear-gradient(90deg, #ff6b35, #ff8c42);
}
.category-card-title {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  text-align: center;
  line-height: 1.4;
}
.explore-more {
  text-align: right;
  margin-top: 16px;
}
.explore-more-link {
  color: #ff6b35;
  text-decoration: none;
  font-size: 14px;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  transition: all 0.3s ease;
}
.explore-more-link:hover {
  color: #e55a2b;
  text-decoration: none;
  transform: translateX(4px);
}
.explore-more-link::after {
  content: '→';
  font-size: 16px;
  transition: transform 0.3s ease;
}
.explore-more-link:hover::after {
  transform: translateX(2px);
}
.faq-section {
  padding: 80px 0;
  text-align: center;
}
.faq-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
}
.faq-header {
  margin-bottom: 60px;
}
.faq-header .section-title {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 16px;
  color: #1e293b;
  text-align: center;
}
.faq-header .section-subtitle {
  font-size: 18px;
  color: #64748b;
  margin: 0;
  text-align: center;
}
.faq-accordion {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.faq-item {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  transition: all 0.3s ease;
  opacity: 0;
  transform: translateY(20px);
  border: 1px solid #f1f5f9;
}
.faq-item.active {
  opacity: 1;
  transform: translateY(0);
  animation: fadeInUp 0.6s ease forwards;
}
.faq-item:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}
.faq-item.expanded {
  border-color: #ff6b35;
  box-shadow: 0 8px 24px rgba(255, 107, 53, 0.15);
}
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.faq-question {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 28px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fff;
}
.faq-question:hover {
  background: #fafbfc;
}
.faq-question h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  text-align: left;
  flex: 1;
  padding-right: 20px;
}
.faq-item.expanded .faq-question {
  background: #fff7ed;
  border-bottom: 1px solid #fed7aa;
}
.faq-icon {
  position: relative;
  width: 32px;
  height: 32px;
  background: #f1f5f9;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
}
.faq-icon .plus,
.faq-icon .minus {
  position: absolute;
  font-size: 20px;
  font-weight: 400;
  color: #64748b;
  transition: all 0.3s ease;
}
.faq-icon .minus {
  opacity: 0;
  transform: rotate(90deg);
}
.faq-item.expanded .faq-icon {
  background: #ff6b35;
  transform: rotate(180deg);
}
.faq-item.expanded .faq-icon .plus,
.faq-item.expanded .faq-icon .minus {
  color: #fff;
}
.faq-item.expanded .faq-icon .plus {
  opacity: 0;
  transform: rotate(90deg);
}
.faq-item.expanded .faq-icon .minus {
  opacity: 1;
  transform: rotate(0deg);
}
.faq-answer {
  max-height: 0;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: #fff;
}
.faq-answer p {
  padding: 0 28px 24px 28px;
  margin: 0;
  font-size: 16px;
  line-height: 1.6;
  color: #64748b;
  text-align: left;
}
.faq-item.expanded .faq-answer {
  max-height: 200px;
  background: #fffbf7;
}
.pre-footer-section {
  padding: 60px 0;
  background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
  border-top: 1px solid #fed7aa;
}
.pre-footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}
.pre-footer-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 40px;
  padding: 40px;
  background: #fff;
  border-radius: 20px;
  box-shadow: 0 12px 40px rgba(255, 107, 53, 0.1);
  border-left: 6px solid #ff6b35;
  position: relative;
  overflow: hidden;
}
.pre-footer-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 107, 53, 0.02) 0%, rgba(255, 140, 66, 0.02) 100%);
  pointer-events: none;
}
.pre-footer-icon {
  font-size: 48px;
  flex-shrink: 0;
  filter: drop-shadow(0 4px 8px rgba(255, 107, 53, 0.2));
  animation: float 3s ease-in-out infinite;
}
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}
.pre-footer-text {
  flex: 1;
  text-align: left;
}
.pre-footer-text h2 {
  font-size: 32px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 12px;
  line-height: 1.2;
}
.pre-footer-text p {
  font-size: 18px;
  color: #64748b;
  margin: 0;
  line-height: 1.5;
}
.pre-footer-button {
  background: linear-gradient(135deg, #ff6b35, #ff8c42);
  color: white;
  padding: 16px 32px;
  border-radius: 12px;
  text-decoration: none;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 6px 20px rgba(255, 107, 53, 0.3);
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
}
.pre-footer-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}
.pre-footer-button:hover {
  background: linear-gradient(135deg, #e55a2b, #ff6b35);
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(255, 107, 53, 0.4);
}
.pre-footer-button:hover::before {
  left: 100%;
}
.footer {
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
  padding: 60px 0 40px;
  margin-top: 0;
}
.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}
.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 60px;
  align-items: start;
}
.footer-brand {
  max-width: 350px;
}
.footer-logo {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.footer-logo img {
  height: 40px;
  margin-right: 12px;
}
.brand-name {
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
}
.footer-description {
  font-size: 14px;
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 24px;
}
.footer-social {
  display: flex;
  gap: 12px;
}
.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: #fff;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  color: #64748b;
  text-decoration: none;
  transition: all 0.3s ease;
}
.social-link:hover {
  background: #ff6b35;
  color: white;
  border-color: #ff6b35;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.2);
}
.footer-section-title {
  font-size: 12px;
  font-weight: 600;
  color: #1e293b;
  letter-spacing: 0.5px;
  margin-bottom: 20px;
  text-transform: uppercase;
}
.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}
.footer-links li {
  margin-bottom: 12px;
}
.footer-links a {
  color: #64748b;
  text-decoration: none;
  font-size: 14px;
  font-weight: 400;
  transition: color 0.3s ease;
  line-height: 1.5;
}
.footer-links a:hover {
  color: #ff6b35;
}
.footer-contact {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.contact-link {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #64748b;
  text-decoration: none;
  font-size: 14px;
  font-weight: 400;
  transition: color 0.3s ease;
  line-height: 1.5;
}
.contact-link:hover {
  color: #ff6b35;
}
.contact-link svg {
  flex-shrink: 0;
  opacity: 0.7;
}
.user-avatar {
  width: 60px;
  height: 60px;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  align-self: center;
}
.user-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #ff6b35;
  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.2);
  transition: all 0.3s ease;
}
.user-avatar img:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(255, 107, 53, 0.3);
}
@media (max-width: 1024px) {
  .mindmap-section {
    flex-direction: column;
    gap: 40px;
    margin-bottom: 20px;
  }
  .mindmap-content {
    max-width: none;
    text-align: center;
  }
  .section-title {
    text-align: center;
  }
  .section-subtitle {
    text-align: center;
  }
  .mindmap-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
  }
  .central-node {
    position: relative;
    top: 0;
    left: 0;
    transform: none;
    margin-bottom: 40px;
    width: 150px;
    height: 150px;
    display: none;
  }
  .feature-node {
    position: relative;
    top: auto;
    left: auto;
    right: auto;
    bottom: auto;
    width: 100%;
    max-width: 500px;
    transform: none !important;
    margin-bottom: 12px;
  }
  .feature-node.active {
    transform: none !important;
  }
  .features-rotating-container{
    width: 100%;
  }
  .node-connector {
    display: none;
  }
  .ai-features-container {
    flex-direction: column;
    gap: 40px;
  }
  .ai-features-content {
    max-width: none;
  }
  .ai-features-content .section-title {
    text-align: center;
  }
  .ai-features-content .section-subtitle {
    text-align: center;
  }
  .comparison-table-container {
    max-width: none;
  }
  .comparison-table {
    font-size: 13px;
  }
  .comparison-table th,
  .comparison-table td {
    padding: 12px 16px;
  }
  .faq-section {
    padding: 60px 0;
  }
  .faq-container {
    padding: 0 16px;
  }
  .faq-header {
    margin-bottom: 40px;
  }
  .faq-header .section-title {
    font-size: 28px;
  }
  .faq-header .section-subtitle {
    font-size: 16px;
  }
  .pre-footer-content {
    flex-direction: column;
    text-align: center;
    gap: 30px;
    padding: 30px;
  }
  .pre-footer-text h2 {
    font-size: 28px;
  }
  .footer-content {
    grid-template-columns: 1fr 1fr;
    gap: 40px;
  }
  .footer-brand {
    grid-column: 1 / -1;
    max-width: none;
    text-align: center;
    margin-bottom: 20px;
  }
}
@media (max-width: 1024px) and (min-width: 769px) {
  .category-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: 18px;
  }
}
@media (max-width: 768px) {
  .nav-links {
    display: none;
  }
  .hero {
    padding: 80px 0 60px;
    min-height: auto;
  }
  .hero-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }
  .hero-image {
    order: 2;
  }
  .hero-text {
    order: 1;
    text-align: center;
  }
  .hero-title {
    font-size: 34px !important;
    margin-bottom: 12px;
  }
  .hero-subtitle-main {
    font-size: 28px !important;
    margin-bottom: 20px;
  }
  .hero-description {
    font-size: 14px !important;
    padding: 0 20px;
    max-width: none;
    margin-bottom: 28px;
  }
  .hero-img {
    max-width: 350px;
  }
  .container {
    padding: 0 16px !important;
  }
  .section-title {
    font-size: 28px;
  }
  .learning-modes-section {
    padding: 60px 0;
  }
  .learning-modes-header {
    margin-bottom: 40px;
  }
  .learning-modes-header .section-title {
    font-size: 24px;
    line-height: 1.3;
  }
  .learning-modes-carousel {
    max-width: 100%;
    margin: 0;
  }
  .carousel-container {
    border-radius: 16px;
    margin: 0 16px;
  }
  .mode-card {
    grid-template-columns: 1fr;
    gap: 30px;
    padding: 40px 30px;
    min-height: auto;
    text-align: center;
  }
  .mode-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }
  .mode-title {
    font-size: 24px;
    margin-bottom: 12px;
  }
  .mode-description {
    font-size: 16px;
    margin-bottom: 20px;
  }
  .mode-image {
    max-width: 280px;
    height: 200px;
    object-fit: contain;
  }
  .carousel-dots {
    margin-top: 30px;
    gap: 10px;
  }
  .dot {
    width: 10px;
    height: 10px;
  }
  .categories-section {
    padding: 60px 0;
  }
  .categories-container {
    padding: 0 16px;
  }
  .categories-header .section-title {
    font-size: 28px;
    line-height: 1.3;
    margin-bottom: 16px;
  }
  .categories-header .section-subtitle {
    font-size: 16px;
  }
  .category-group {
    margin-bottom: 40px;
  }
  .category-title {
    font-size: 22px;
    margin-bottom: 20px;
  }
  .category-cards {
    grid-template-columns: 1fr;
    gap: 16px;
    margin-bottom: 16px;
  }
  .category-card {
    padding: 20px 16px;
  }
  .category-card-title {
    font-size: 16px;
  }
  .faq-section {
    padding: 50px 0;
  }
  .faq-header .section-title {
    font-size: 24px;
    line-height: 1.3;
  }
  .faq-header .section-subtitle {
    font-size: 15px;
  }
  .faq-question {
    padding: 20px 24px;
  }
  .faq-question h3 {
    font-size: 16px;
    padding-right: 16px;
  }
  .faq-icon {
    width: 28px;
    height: 28px;
  }
  .faq-icon .plus,
  .faq-icon .minus {
    font-size: 18px;
  }
  .faq-answer p {
    padding: 0 24px 20px 24px;
    font-size: 15px;
  }
  .pre-footer-section {
    padding: 40px 0;
    margin-top: 60px;
  }
  .pre-footer-content {
    padding: 24px;
    gap: 24px;
  }
  .pre-footer-icon {
    font-size: 36px;
  }
  .pre-footer-text h2 {
    font-size: 24px;
    line-height: 1.3;
  }
  .pre-footer-text p {
    font-size: 16px;
  }
  .pre-footer-button {
    padding: 14px 24px;
    font-size: 15px;
    width: 100%;
    text-align: center;
  }
  .footer {
    padding: 40px 0 30px;
  }
  .footer-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  .footer-brand {
    margin-bottom: 0;
  }
  .footer-social {
    justify-content: center;
  }
  .footer-section-title {
    margin-bottom: 16px;
  }
  .footer-links li {
    margin-bottom: 8px;
  }
  .user-avatar {
    width: 50px;
    height: 50px;
    margin-bottom: 12px;
  }
  .user-avatar img {
    border-width: 2px;
  }
}
