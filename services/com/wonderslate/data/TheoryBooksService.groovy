package com.wonderslate.data

import com.wonderslate.logs.AutogptErrorLoggerService
import com.wonderslate.shop.PdfExporterService
import grails.converters.JSON
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import org.grails.web.json.JSONObject
import org.springframework.transaction.annotation.Propagation
import java.util.concurrent.*
import grails.async.Promises
import static grails.async.Promises.task
import org.apache.http.impl.client.CloseableHttpClient
import org.apache.http.impl.client.HttpClients
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager
import org.apache.http.client.methods.HttpPost
import org.apache.http.entity.StringEntity
import org.apache.http.util.EntityUtils
import javax.annotation.PreDestroy

class TheoryBooksService {
    def grailsApplication
    PdfExporterService pdfExporterService
    def springSecurityService
    AutogptService autogptService
    AutogptErrorLoggerService autogptErrorLoggerService
    def redisService

    // Connection pool for HTTP requests - no timeouts for LLM calls
    private static final PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager()
    private static final CloseableHttpClient httpClient

    // Thread pool for async operations
    private final ExecutorService executorService = Executors.newFixedThreadPool(3)

    // Cache for frequently accessed data
    private final Map<String, Prompts> promptsCache = new ConcurrentHashMap<>()

    static {
        connectionManager.setMaxTotal(20)
        connectionManager.setDefaultMaxPerRoute(5)

        httpClient = HttpClients.custom()
            .setConnectionManager(connectionManager)
            .disableConnectionState() // For better performance
            .build()
    }

    def talkToGPT(String prompt, String apiEndpoint = "chat-completion") {
        try {
            String url = grailsApplication.config.grails.aiserver.url + "/" + apiEndpoint
            HttpPost httpPost = new HttpPost(url)

            // Set headers
            httpPost.setHeader("Content-Type", "application/json")

            // Create request body
            JSONObject requestBody = new JSONObject()
            requestBody.put("prompt", prompt)
            StringEntity entity = new StringEntity(requestBody.toString(), "UTF-8")
            httpPost.setEntity(entity)

            // Execute request with no timeout (LLM responses can be very long)
            def response = httpClient.execute(httpPost)

            try {
                if (response.getStatusLine().getStatusCode() == 200) {
                    String responseBody = EntityUtils.toString(response.getEntity())
                    def jsonSlurper = new JsonSlurper()
                    try {
                        def jsonResponse = jsonSlurper.parseText(responseBody)
                        return jsonResponse
                    } catch (Exception e) {
                        println("Exception parsing GPT response: " + (e.toString().length() > 1000 ? e.toString().substring(0, 1000) : e.toString()))
                        return null
                    }
                } else {
                    println("Error in GPT call: " + response.getStatusLine().getStatusCode())
                    return null
                }
            } finally {
                response.close()
            }
        } catch (Exception e) {
            println("Exception in talkToGPT: " + (e.toString().length() > 1000 ? e.toString().substring(0, 1000) : e.toString()))
            return null
        }
    }

    def jsonCleaner(String jsonInput){
        if (!jsonInput) return jsonInput

        jsonInput = jsonInput.replaceAll("`", "")
        jsonInput = jsonInput.replaceAll("json", "")
        jsonInput = jsonInput.replaceAll("html", "")
        jsonInput = jsonInput.replaceAll("\n", "")
        jsonInput = jsonInput.replaceAll("\r", "")

        return jsonInput
    }

    // Cache prompts to avoid repeated DB queries
    private Prompts getCachedPrompt(String promptType) {
        return promptsCache.computeIfAbsent(promptType) {
            Prompts.findByPromptType(promptType)
        }
    }

    // Batch fetch related data to avoid N+1 queries
    private Map<String, Object> fetchChapterData(String chapterId) {
        // Skip Redis caching for now to avoid serialization issues
        // Can be re-enabled later with proper serialization handling
        ChaptersMst chaptersMst = ChaptersMst.findById(new Long(chapterId))
        BooksDtl booksDtl = BooksDtl.findByBookId(chaptersMst.bookId)
        List<ChaptersSubtopicDtl> subtopics = ChaptersSubtopicDtl.findAllByChapterId(new Long(chapterId))

        return [
            chapter: chaptersMst,
            book: booksDtl,
            subtopics: subtopics
        ]
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    def createChapterContents(params){
        String chapterId = params.chapterId
        ResourceDtl resourceDtl = ResourceDtl.findByChapterIdAndResType(new Long(chapterId),"Notes")
        if(resourceDtl!=null) return [status: "OK"]

        // Fetch all related data in one go to avoid N+1 queries
        Map<String, Object> chapterData = fetchChapterData(chapterId)
        ChaptersMst chaptersMst = chapterData.chapter
        BooksDtl booksDtl = chapterData.book
        List<ChaptersSubtopicDtl> subtopics = chapterData.subtopics

        // Cache prompts
        Prompts subtopicPrompts = getCachedPrompt("subtopicContentCreator")

        File srcDir = new File(grailsApplication.config.grails.basedir.path + "/supload/books/theoryBooks/"+chaptersMst.bookId)
        if(!srcDir.exists()) srcDir.mkdirs()
        File chapterHtmlFile = new File(srcDir, "chapter_"+chapterId+".html")

        // Pre-fetch PYQ resource to avoid repeated queries
        ResourceDtl pyqResourceDtl = ResourceDtl.findByChapterIdAndResTypeAndResourceName(new Long(chapterId),"QA","PYQs")

        // Use StringBuilder for efficient string concatenation
        StringBuilder htmlContentBuilder = new StringBuilder()

        // Prepare data for LLM calls in main thread (with database access)
        def subtopicData = subtopics.collect { subtopic ->
            String questionText = ""
            if (pyqResourceDtl) {
                List<ObjectiveMst> questions = ObjectiveMst.findAllByQuizIdAndSubTopic(new Integer(pyqResourceDtl.resLink), subtopic.title)
                StringBuilder questionBuilder = new StringBuilder()
                questions.eachWithIndex { question, index ->
                    questionBuilder.append("${index + 1}. ${question.question}\n")
                }
                questionText = questionBuilder.toString()
            }

            String prompt = subtopicPrompts.basePrompt
            prompt = prompt.replaceAll("SUBTOPIC", subtopic.title+" "+subtopic.keyConcepts+". "+subtopic.learningObjective)
            prompt = prompt + "\n PYQs: \n" + questionText

            return [
                subtopic: subtopic,
                prompt: prompt,
                subtopicTitle: subtopic.title
            ]
        }

        // Process LLM calls in parallel (no database operations in async threads)
        def llmPromises = subtopicData.collect { data ->
            task {
                try {
                    println("Processing subtopic: " + data.subtopicTitle)
                    println("Calling GPT for subtopic: " + data.subtopicTitle)

                    // Only LLM call in async thread - no database operations
                    def responseString = talkToGPT(data.prompt)

                    return [
                        success: true,
                        subtopic: data.subtopic,
                        response: responseString?.response,
                        subtopicTitle: data.subtopicTitle
                    ]
                } catch (Exception e) {
                    println("Error processing subtopic ${data.subtopicTitle}: ${e.message}")
                    return [
                        success: false,
                        subtopic: data.subtopic,
                        error: e.message,
                        subtopicTitle: data.subtopicTitle
                    ]
                }
            }
        }

        // Wait for all LLM calls to complete
        def llmResults = llmPromises.collect { it.get() }

        // Process results sequentially in main session (database operations)
        llmResults.each { result ->
            if (result.success && result.response) {
                try {
                    String content = jsonCleaner(result.response)
                    htmlContentBuilder.append(content)
                    println("Successfully processed subtopic: ${result.subtopicTitle}")
                } catch (Exception e) {
                    println("Error processing result for subtopic ${result.subtopicTitle}: ${e.message}")
                }
            } else {
                println("Failed to process subtopic: ${result.subtopicTitle} - ${result.error ?: 'No response'}")
            }
        }

        // Generate introduction synchronously to avoid session issues
        Prompts introPrompts = getCachedPrompt("chapterIntroductionCreator")
        String introPrompt = introPrompts.basePrompt
        introPrompt = introPrompt.replaceAll("CHAPTERTITLE", chaptersMst.name)
        introPrompt = introPrompt + " \n" + htmlContentBuilder.toString()
        println("Calling GPT for introduction")
        def responseString = talkToGPT(introPrompt)
        String introContent = responseString?.response ? jsonCleaner(responseString.response) : ""

        String finalHtmlContent = introContent + htmlContentBuilder.toString()

        // Create resource record (database operation - keep in main thread)
        resourceDtl = ResourceDtl.findByChapterIdAndResType(new Long(chapterId),"Notes")
        if(resourceDtl==null) {
            resourceDtl = new ResourceDtl(chapterId: new Long(chapterId), resType: "Notes", resourceName: chaptersMst.name, createdBy:"System" , resLink: "blank")
            resourceDtl.save(failOnError: true, flush: true)
        }

        // File operations can be done synchronously for now to avoid session issues
        chapterHtmlFile.write(finalHtmlContent)

        // PDF generation - keep synchronous to avoid session issues
        pdfExporterService.generateTheoryBookPdf(""+chapterId,""+resourceDtl.id)

        resourceDtl.resLink = "supload/books/"+chaptersMst.bookId+"/chapters/"+chapterId+"/"+resourceDtl.id+"/"+resourceDtl.id+".pdf"
        resourceDtl.filename = resourceDtl.id+".pdf"
        resourceDtl.save(failOnError: true, flush: true)

        return [status: "OK"]
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    def getPYQsForChapter(params){
        String chapterId = params.chapterId
        ResourceDtl resourceDtl = ResourceDtl.findByChapterIdAndResTypeAndResourceName(new Long(chapterId),"QA","PYQs")
        if(resourceDtl!=null) return [status: "OK"]

        // Fetch all related data efficiently
        Map<String, Object> chapterData = fetchChapterData(chapterId)
        ChaptersMst chaptersMst = chapterData.chapter
        BooksDtl booksDtl = chapterData.book
        List<ChaptersSubtopicDtl> subtopics = chapterData.subtopics

        Prompts prompts = getCachedPrompt("pyqsForSubtopic")

        // Create resource once outside the loop
        QuizIdGenerator quizIdGenerator = new QuizIdGenerator()
        quizIdGenerator.save()
        resourceDtl = new ResourceDtl(chapterId: new Long(chapterId), resType: "QA", resourceName: "PYQs",
                resLink: quizIdGenerator.id, createdBy: "System")
        resourceDtl.save(failOnError: true, flush: true)

        // Prepare data for LLM calls in main thread (with database access)
        def subtopicData = subtopics.collect { subtopic ->
            String prompt = prompts.basePrompt
            prompt = prompt.replaceAll("SYLLABUSSUBJECT", booksDtl.syllabusSubject ?: "")
            prompt = prompt.replaceAll("UNIVERSITY", booksDtl.university ?: "")
            prompt = prompt.replaceAll("SUBTOPIC", subtopic.title+" "+subtopic.keyConcepts)

            return [
                subtopic: subtopic,
                prompt: prompt,
                subtopicTitle: subtopic.title
            ]
        }

        // Process LLM calls in parallel (no database operations in async threads)
        def llmPromises = subtopicData.collect { data ->
            task {
                try {
                    // Only LLM call in async thread - no database operations
                    def responseString = talkToGPT(data.prompt,"perplexity-completion")

                    return [
                        success: true,
                        subtopic: data.subtopic,
                        response: responseString?.response,
                        subtopicTitle: data.subtopicTitle
                    ]
                } catch (Exception e) {
                    println("Error processing subtopic ${data.subtopicTitle}: ${e.message}")
                    return [
                        success: false,
                        subtopic: data.subtopic,
                        error: e.message,
                        subtopicTitle: data.subtopicTitle
                    ]
                }
            }
        }

        // Wait for all LLM calls to complete
        def llmResults = llmPromises.collect { it.get() }

        // Process results sequentially in main session (database operations)
        def totalQuestions = 0
        llmResults.each { result ->
            if (result.success && result.response) {
                try {
                    def jsonResponse
                    try {
                        jsonResponse = new JsonSlurper().parseText(jsonCleaner(result.response))
                    } catch (Exception e) {
                        try {
                            jsonResponse = new JsonSlurper().parseText(autogptService.fixJSONFromLLM(result.response))
                        } catch (Exception e2) {
                            println("Failed to parse JSON for subtopic ${result.subtopicTitle}: ${e2.toString()}")
                            return
                        }
                    }

                    // Insert questions for this subtopic (database operations in main session)
                    jsonResponse.each { question ->
                        ObjectiveMst objectiveMst = new ObjectiveMst(
                            quizId: new Integer(resourceDtl.resLink),
                            quizType: "QA",
                            question: question.question,
                            difficultylevel: question.difficulty,
                            qType: question.type,
                            subTopic: result.subtopic.title,
                            explainLink: "${question.university ?: ''},${question.year ?: ''}"
                        )
                        objectiveMst.save(failOnError: true, flush: false) // Batch flush
                        totalQuestions++
                    }
                    println("Successfully processed ${jsonResponse.size()} questions for subtopic: ${result.subtopicTitle}")
                } catch (Exception e) {
                    println("Error processing result for subtopic ${result.subtopicTitle}: ${e.message}")
                }
            } else {
                println("Failed to process subtopic: ${result.subtopicTitle} - ${result.error ?: 'No response'}")
            }
        }

        // Flush all saves at once
        ObjectiveMst.withSession { session ->
            session.flush()
        }

        println("Total questions created: ${totalQuestions}")
        return [status: "OK", noOfQuestions: totalQuestions]
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    def getPYQsForBoard(params){
        String chapterId = params.chapterId
        ResourceDtl resourceDtl = ResourceDtl.findByChapterIdAndResTypeAndResourceName(new Long(chapterId),"QA","PYQs")
        if(resourceDtl!=null) return [status: "OK"]

        // Fetch chapter data efficiently
        Map<String, Object> chapterData = fetchChapterData(chapterId)
        ChaptersMst chaptersMst = chapterData.chapter
        BooksDtl booksDtl = chapterData.book

        ResourceDtl readingMaterialResourceDtl = ResourceDtl.findByChapterIdAndResTypeAndGptResourceTypeIsNull(new Long(chapterId),"Notes")
        String extractPath = readingMaterialResourceDtl?.extractPath
        println("extractPath: for chapter ${chapterId} is " + extractPath)

        if (!extractPath || extractPath.trim().isEmpty()) {
            println("extractPath is empty for chapter ${chapterId} and calling storePdfVectors")
            autogptService.storePdfVectors(params)
            extractPath = "supload/pdfextracts/"+chaptersMst.bookId+"/"+chaptersMst.id+"/"+readingMaterialResourceDtl.id+"/extractedImages/"+readingMaterialResourceDtl.id+".txt"
        }

        String folderPath = extractPath.substring(0, extractPath.lastIndexOf("/"))
        File metadataFile = new File(grailsApplication.config.grails.basedir.path + "/" + folderPath + "/chapterMetadata" + chaptersMst.id + ".txt")
        if(!metadataFile.exists()) {
            autogptService.getChapterMetaData(params)
        }

        // Parse metadata with better error handling
        def json
        try {
            String metadataString = metadataFile.text
            metadataString = jsonCleaner(metadataString)
            json = new JsonSlurper().parseText(metadataString)
        } catch (Exception e) {
            try {
                String metadataString = metadataFile.text
                metadataString = autogptService.fixJSONFromLLM(metadataString)
                json = new JsonSlurper().parseText(metadataString)
            } catch (Exception e2) {
                println("Failed to parse metadata for chapter ${chapterId}: ${e2.toString()}")
                autogptErrorLoggerService.createLog(new Long(chapterId), null, "getPYQsForBoard", "Exception in parsing metadata", metadataFile.text)
                return [status: "Error", message: "Failed to parse chapter metadata"]
            }
        }

        def subtopics = json.metadata?.subtopics
        if (!subtopics) {
            println("No subtopics found in metadata for chapter " + chapterId)
            return [status: "Error", message: "No subtopics found in metadata"]
        }

        println("No of subtopics are "+subtopics.size()+" for chapter " + chaptersMst.id)
        Prompts prompts = getCachedPrompt("pyqsForBoardExams")

        // Clean up existing PYQs if they exist
        ResourceDtl.withSession { session ->
            session.clear()
        }
        resourceDtl = ResourceDtl.findByChapterIdAndResTypeAndResourceName(new Long(chapterId),"QA","PYQs")
        if(resourceDtl!=null) {
            println("PYQs already created for chapter " + chapterId+" and we will delete them first")
            ObjectiveMst.executeUpdate("delete from ObjectiveMst where quizId = :quizId", [quizId: resourceDtl.resLink])
        }

        // Create resource once outside the loop
        if (resourceDtl == null) {
            QuizIdGenerator quizIdGenerator = new QuizIdGenerator()
            quizIdGenerator.save()
            resourceDtl = new ResourceDtl(chapterId: new Long(chapterId), resType: "QA", resourceName: "PYQs",
                    resLink: quizIdGenerator.id, createdBy: "System")
            resourceDtl.save(failOnError: true, flush: true)
        }

        def totalQuestions = 0
        StringBuilder htmlContentBuilder = new StringBuilder()

        // Prepare data for LLM calls in main thread (with database access)
        def subtopicData = subtopics.collect { subtopic ->
            String prompt = prompts.basePrompt
            prompt = prompt.replaceAll("SYLLABUSSUBJECT", params.subjectSyllabus ?: "")
            prompt = prompt.replaceAll("TARGETBOARD", params.boardExam ?: "")
            prompt = prompt + " " + (subtopic.heading ?: "") + " " + (subtopic.keyConcepts ?: "")

            return [
                subtopic: subtopic,
                prompt: prompt,
                subtopicHeading: subtopic.heading ?: "",
                subtopicKeyConcepts: subtopic.keyConcepts ?: ""
            ]
        }

        // Process LLM calls in parallel (no database operations in async threads)
        def llmPromises = subtopicData.collect { data ->
            task {
                try {
                    // Only LLM call in async thread - no database operations
                    def responseString = talkToGPT(data.prompt, "perplexity-completion")

                    return [
                        success: true,
                        subtopic: data.subtopic,
                        response: responseString?.response,
                        subtopicHeading: data.subtopicHeading,
                        subtopicKeyConcepts: data.subtopicKeyConcepts
                    ]
                } catch (Exception e) {
                    println("Exception in getPYQsForBoard for subtopic ${data.subtopicHeading} and chapter ${chapterId}: ${e.toString()}")
                    return [
                        success: false,
                        subtopic: data.subtopic,
                        error: e.message,
                        subtopicHeading: data.subtopicHeading
                    ]
                }
            }
        }

        // Wait for all LLM calls to complete
        def llmResults = llmPromises.collect { it.get() }

        // Process results sequentially in main session (database operations)
        llmResults.each { result ->
            if (result.success && result.response) {
                try {
                    def jsonResponse
                    try {
                        jsonResponse = new JsonSlurper().parseText(jsonCleaner(result.response))
                    } catch (Exception e) {
                        try {
                            jsonResponse = new JsonSlurper().parseText(autogptService.fixJSONFromLLM(result.response))
                        } catch (Exception e2) {
                            autogptErrorLoggerService.createLog(new Long(chapterId), resourceDtl.id, "pyqsForBoardExams", "Exception in parsing responseAnswer", result.response)
                            println("Failed to parse JSON for subtopic ${result.subtopicHeading}: ${e2.toString()}")
                            return
                        }
                    }

                    // Prepare questions and content (database operations in main session)
                    StringBuilder questionsBuilder = new StringBuilder()
                    int questionNumber = 1

                    jsonResponse.each { question ->
                        questionsBuilder.append("${questionNumber}. ${question.question}\n")
                        ObjectiveMst objectiveMst = new ObjectiveMst(
                            quizId: new Integer(resourceDtl.resLink),
                            quizType: "QA",
                            question: question.question,
                            difficultylevel: question.difficulty,
                            qType: question.type,
                            subTopic: result.subtopic.title,
                            explainLink: "${question.board ?: ''},${question.year ?: ''}"
                        )
                        objectiveMst.save(failOnError: true, flush: false)
                        totalQuestions++
                        questionNumber++
                    }

                    String questionsText = questionsBuilder.toString()
                    String subtopicContent = result.subtopicHeading + " " + result.subtopicKeyConcepts

                    // Generate enhanced metadata and content
                    String enhancedMetadata = createEnhancedMetadata(questionsText, subtopicContent)
                    String htmlContent = createSubTopicContents(subtopicContent, questionsText, enhancedMetadata)

                    htmlContentBuilder.append(htmlContent)
                    println("Successfully processed subtopic: ${result.subtopicHeading} with ${jsonResponse.size()} questions")
                } catch (Exception e) {
                    println("Error processing result for subtopic ${result.subtopicHeading}: ${e.message}")
                }
            } else {
                println("Failed to process subtopic: ${result.subtopicHeading} - ${result.error ?: 'No response'}")
            }
        }

        // Flush all database operations
        ObjectiveMst.withSession { session ->
            session.flush()
        }
        // File operations - keep synchronous to avoid session issues
        String finalHtmlContent = htmlContentBuilder.toString()
        File srcDir = new File(grailsApplication.config.grails.basedir.path + "/supload/books/theoryBooks/"+chaptersMst.bookId)
        if(!srcDir.exists()) srcDir.mkdirs()
        File chapterHtmlFile = new File(srcDir, "chapter_"+chapterId+".html")

        chapterHtmlFile.write(finalHtmlContent)

        // Create ExamNotes resource (database operation - keep in main thread)
        ResourceDtl examNotesResourceDtl = ResourceDtl.findByChapterIdAndResType(new Long(chapterId),"ExamNotes")
        if(examNotesResourceDtl==null) {
            examNotesResourceDtl = new ResourceDtl(chapterId: new Long(chapterId), resType: "ExamNotes", resourceName: chaptersMst.name, createdBy: "System", resLink: "blank")
            examNotesResourceDtl.save(failOnError: true, flush: true)
        }

        // Generate PDF synchronously to avoid session issues
        println("Generating PDF for chapter "+chaptersMst.name)
        pdfExporterService.generateTheoryBookPdf(""+chapterId,""+examNotesResourceDtl.id)

        examNotesResourceDtl.resLink = "supload/books/"+chaptersMst.bookId+"/chapters/"+chapterId+"/"+examNotesResourceDtl.id+"/"+examNotesResourceDtl.id+".pdf"
        examNotesResourceDtl.filename = examNotesResourceDtl.id+".pdf"
        examNotesResourceDtl.save(failOnError: true, flush: true)

        println("PYQs created for chapter "+chapterId+" and no of questions are "+totalQuestions)
        return [status: "OK", noOfQuestions: totalQuestions]
    }

    def createEnhancedMetadata(String pyqQuestions,String subtopicContents){
        Prompts prompts = getCachedPrompt("metaDataEnhancer")
        String prompt = prompts.basePrompt
        prompt += "\n"+subtopicContents
        prompt += "\n The PYQs are"+pyqQuestions
        def responseString = talkToGPT(prompt)
        return responseString?.response ? jsonCleaner(responseString.response) : ""
    }

    def createSubTopicContents(String subtopicContents,String pyqQuestions,String enhancedMetadata){
        Prompts prompts = getCachedPrompt("examNotesForSubtopic")
        String prompt = prompts.basePrompt
        prompt = prompt + "\n"+subtopicContents
        prompt += "\n The PYQs are "+pyqQuestions
        prompt += "\n The PYQ pattern is "+enhancedMetadata
        def responseString = talkToGPT(prompt)
        return responseString?.response ? jsonCleaner(responseString.response) : ""
    }

    @PreDestroy
    void cleanup() {
        try {
            executorService.shutdown()
            if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                executorService.shutdownNow()
            }
        } catch (InterruptedException e) {
            executorService.shutdownNow()
            Thread.currentThread().interrupt()
        }

        try {
            httpClient.close()
        } catch (Exception e) {
            println("Error closing HTTP client: ${e.message}")
        }
    }
}
