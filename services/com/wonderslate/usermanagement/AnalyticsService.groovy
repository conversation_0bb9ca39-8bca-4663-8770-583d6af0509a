package com.wonderslate.usermanagement

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.ResourceDtl
import com.wonderslate.prepjoy.DailyTestsMst
import com.wonderslate.prepjoy.QuizRecDtl
import com.wonderslate.prepjoy.QuizRecMst
import grails.transaction.Transactional
import groovy.sql.Sql

@Transactional
class AnalyticsService {

    def springSecurityService
    def redisService
    def grailsApplication
    DataProviderService dataProviderService

    def getUsersLastQuiz(String username){
        List quizRecMsts  = QuizRecMst.findAllByUsername(username, [sort:"id", order:"desc", max:1])
        String quizName = "Current Affairs"

        if(quizRecMsts.size()>0){
            quizName = getQuizName(quizRecMsts[0])
            List latestQuizInfo = quizRecMsts.collect { quizRecMst ->
                return [quizName:quizName, resId:quizRecMst.resId, quizId:quizRecMst.quizId, points:quizRecMst.points, matchStatus:  quizRecMst.matchStatus,
                userTime:quizRecMst.userTime,dateCreated:quizRecMst.dateCreated,language:quizRecMst.language,quizType:quizRecMst.quizType, noOfQuestions:quizRecMst.noOfQuestions,
                        dailyTestDtlId:quizRecMst.dailyTestDtlId,currentAffairsType:quizRecMst.currentAffairsType,realDailyTestDtlId:quizRecMst.realDailyTestDtlId,quizRecId:quizRecMst.id,
                        status:quizRecMst.status!=null?quizRecMst.status:""]
            }
            Gson gson = new Gson();
            String element = gson.toJson(latestQuizInfo,new TypeToken<List>() {}.getType())
            redisService.("latestQuizInfo_"+username) = element

        }else{
            redisService.("latestQuizInfo_"+username) = "no records"
        }

    }

    def getUsersHistoryForAQuiz(String username, String quizId, String quizType) {

        List quizRecMsts
        String quizName = "Current Affairs"
        List quizRecDtl

        String sql
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        if("dailyTests".equals(quizType))
        {
            //quizId is actually the realQuizId
            quizRecMsts = QuizRecMst.findAllByUsernameAndRealDailyTestDtlId(username,new Integer(quizId),[sort:"id", order:"desc"])
        }
        else{
            quizRecMsts = QuizRecMst.findAllByUsernameAndResId(username,new Integer(quizId),[sort:"id", order:"desc"])
        }
     if(quizRecMsts.size()>0){
            quizName = getQuizName(quizRecMsts[0])
            List latestQuizInfo = quizRecMsts.collect { quizRecMst ->
                quizName = getQuizName(quizRecMst)
                int noOfCorrectAnswers = 0;
                int noOfInCorrectAnswers = 0;
                int noOfSkipped = 0;


                if(quizRecMst.correctAnswers==null||(quizRecMst.correctAnswers.intValue()==0&&quizRecMst.incorrectAnswers.intValue()==0&&quizRecMst.skipped.intValue()==0)) {
                    def sql1 = new Sql(dataSource)
                    //for sql data
                    sql = "select * from quiz_rec_dtl where quiz_rec_id=" + quizRecMst.id + " order by id"

                    def results = sql1.rows(sql);
                    List userAnswers = results.collect { userAnswer ->
                        if (userAnswer.correct_option == userAnswer.user_option) noOfCorrectAnswers++;
                        if ((userAnswer.correct_option != userAnswer.user_option) && (userAnswer.user_option != '-1')) noOfInCorrectAnswers++;
                        if (userAnswer.user_option == '-1') noOfSkipped++;
                    }

                    //updating the quiz rec mst, for optimisation purpose
                    quizRecMst.correctAnswers = new Integer(noOfCorrectAnswers)
                    quizRecMst.incorrectAnswers = new Integer(noOfInCorrectAnswers)
                    quizRecMst.skipped = new Integer(noOfSkipped)
                    quizRecMst.save(failOnError: true, flush: true)


                }

                noOfCorrectAnswers = quizRecMst.correctAnswers.intValue()
                noOfInCorrectAnswers = quizRecMst.incorrectAnswers.intValue()
                noOfSkipped = quizRecMst.skipped.intValue()

                return [quizName:quizName, resId:quizRecMst.resId, quizId:quizRecMst.quizId, points:quizRecMst.points, matchStatus:  quizRecMst.matchStatus,
                        userTime:quizRecMst.userTime,dateCreated:quizRecMst.dateCreated,language:quizRecMst.language,quizType:quizRecMst.quizType, noOfQuestions:quizRecMst.noOfQuestions,
                        dailyTestDtlId:quizRecMst.dailyTestDtlId,currentAffairsType:quizRecMst.currentAffairsType,realDailyTestDtlId:quizRecMst.realDailyTestDtlId,quizRecId:quizRecMst.id,
                        status:quizRecMst.status!=null?quizRecMst.status:"",correctAnsCount:noOfCorrectAnswers,inCorrectAnsCount:noOfInCorrectAnswers,skippedAnsCount:noOfSkipped]
            }
            Gson gson = new Gson();
            String element = gson.toJson(latestQuizInfo,new TypeToken<List>() {}.getType())
            if("dailyTests".equals(quizType))
            redisService.("userQuizHistoryFor_"+quizType+"_"+username+"_"+quizId) = element
            else redisService.("userQuizHistoryFor_regular_"+username+"_"+quizId) = element

        }
    }

    def totalQuizzesAttempted(String username){
         int totalCount =  QuizRecMst.countByUsername(username)
         if(totalCount>0) redisService.("userTotalQuizCount_"+username) = ""+totalCount
         else redisService.("userTotalQuizCount_"+username)  = "no records"
    }
    def getQuizName(QuizRecMst quizRecMst){
        String quizName = "Current Affairs"
        if(quizRecMst.resId!=null){
            ResourceDtl resourceDtl = dataProviderService.getResourceDtl(quizRecMst.resId)
            if(resourceDtl!=null) {
                quizName = resourceDtl.resourceName
            }
        }else if(quizRecMst.dailyTestDtlId!=null){
            DailyTestsMst dailyTestsMst = dataProviderService.getDailyTestsMst(quizRecMst.dailyTestDtlId)
            quizName = dailyTestsMst.testName
        }

        return quizName
    }

    def getUsersHistoryForAllQuizzes(String username, String startIndex) {

        String quizName = "Current Affairs"
        List   quizRecMsts = QuizRecMst.findAllByUsername(username,[sort:"id", order:"desc",max: 20, offset: startIndex])
        List quizHistoryList = null

        if(quizRecMsts.size()>0){

            quizHistoryList = quizRecMsts.collect { quizRecMst ->
                quizName = getQuizName(quizRecMst)
                return [quizName:quizName, resId:quizRecMst.resId, quizId:quizRecMst.quizId, points:quizRecMst.points, matchStatus:  quizRecMst.matchStatus,
                        userTime:quizRecMst.userTime,dateCreated:quizRecMst.dateCreated,language:quizRecMst.language,quizType:quizRecMst.quizType, noOfQuestions:quizRecMst.noOfQuestions,
                        dailyTestDtlId:quizRecMst.dailyTestDtlId,currentAffairsType:quizRecMst.currentAffairsType,realDailyTestDtlId:quizRecMst.realDailyTestDtlId,quizRecId:quizRecMst.id,
                status:quizRecMst.status!=null?quizRecMst.status:""]
            }

        }

        return quizHistoryList
    }
}
