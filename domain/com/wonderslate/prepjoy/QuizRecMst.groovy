package com.wonderslate.prepjoy

class QuizRecMst {

    Integer resId
    String username
    Integer points
    String matchStatus
    Double userTime
    Date dateCreated
    String challenger
    Integer challengerPoints
    Double challengerTime
    Integer siteId
    String source
    Integer quizId
    String language
    String quizType
    Integer noOfQuestions
    Integer dailyTestDtlId
    String currentAffairsType
    Integer realDailyTestDtlId
    Integer userChallengeId
    String status
    Integer testGenId
    Integer instituteId
    Integer correctAnswers
    Integer incorrectAnswers
    Integer skipped
    Integer parent_quiz_rec_id
    String retestType
    Double userScore


    static constraints = {
        challenger blank: true, nullable: true
        challengerPoints blank: true, nullable: true
        challengerTime blank: true, nullable: true
        source blank: true, nullable: true
        matchStatus blank: true, nullable: true
        language blank: true, nullable: true
        resId blank: true, nullable: true
        quizId blank: true, nullable: true
        quizType blank: true, nullable: true
        noOfQuestions blank: true, nullable: true
        dailyTestDtlId blank: true, nullable: true
        currentAffairsType blank: true, nullable: true
        realDailyTestDtlId blank: true, nullable: true
        userChallengeId blank: true, nullable: true
        status blank: true, nullable: true
        testGenId blank: true, nullable: true
        instituteId blank: true, nullable: true
        correctAnswers blank: true, nullable: true
        incorrectAnswers blank: true, nullable: true
        skipped blank: true, nullable: true
        parent_quiz_rec_id blank: true, nullable: true
        retestType blank: true, nullable: true
        userScore blank: true, nullable: true
    }

    static mapping = {
        datasource 'wsuser'
    }
}
