package com.wonderslate.log

class AutogptLog {

    String createdBy
    Date dateCreated
    Date dateStarted
    Date dateCompleted
    String gptStatus
    Long chapterId
    Integer attempts
    String createSnapshot
    Integer timesAborted
    Integer serverIndex
    String subjectSyllabus
    String boardExam
    String higherPriority


    static constraints = {
        dateCompleted blank: true, nullable: true
        gptStatus blank: true, nullable: true
        attempts blank: true, nullable: true
        dateStarted blank: true, nullable: true
        createSnapshot blank: true, nullable: true
        timesAborted blank: true, nullable: true
        serverIndex blank: true, nullable: true
        subjectSyllabus blank: true, nullable: true
        boardExam blank: true, nullable: true
        higherPriority blank: true, nullable: true

    }
    static mapping = {
        datasource 'wslog'
    }
}
